import mysql.connector


class DBUtil:
    """This class acts as an DB Util."""

    def __init__(self, config_dict):
        """This is a init function to initialize."""
        try:
            self.conn = mysql.connector.connect(
                host=config_dict["host"],
                user=config_dict["user"],
                password=config_dict["password"],
                database=config_dict["database"],
            )
            print("Database connection is success.")
        except Exception as exc:
            print("Database connection is failed.")
            print(str(exc))

    async def execute_one(self, query, params):
        """This function is to execute the select
        query and fetch one result."""
        cursor = self.conn.cursor()
        try:
            cursor.execute(query, params)
            result = cursor.fetchone()
            if result is None:
                raise Exception("Data not found.")
            field_names = [i[0] for i in cursor.description]
            return dict(zip(field_names, result))
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            cursor.close()

    async def execute_all(self, query, params):
        """This function is to execute the select
        query and fetch all result."""
        cursor = self.conn.cursor()
        try:
            cursor.execute(query, params)
            results = cursor.fetchall()
            field_names = [i[0] for i in cursor.description]
            return [dict(zip(field_names, row)) for row in results]
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            cursor.close()

    async def execute_cud(self, query, params):
        """This function is to insert a new record."""
        cursor = self.conn.cursor(dictionary=True)
        try:
            cursor.execute(query, params)
            self.conn.commit()
        except Exception as exc:
            self.conn.rollback()
            print(str(exc))
            raise exc
        finally:
            cursor.close()
