1. Get the machine login details (mqtt, subscribe_topic, producer_topic)
2. Connect and check with local setup
3. Check both the topics
4. In what message format they are sending the data (? 32 bit hash data)
    - Will get it
5. Are they configuring topic and pwds while creating machine ? 
    Machine will be assembled in Chennai office. That time, they are configuring the MQTT details. 
    - cpp code
6. Can we reconfigure/reset it ? 
    Yes, they can. But in person the technical person has to configure in the machine.
7. How to check if the machine is active or not ?
    - Getid - Yet to check with older team support

8. Currency input : Note Notein topics - Firmware - Machine code - Constant topics
9. NFC - ? 
10. In old machines, the QR code will be displayed in the costly display. So no need to handle here.

Cases: 

Stock Empty - 300 minutes once
    reload
Send Error Message
    error
send_coin_note_UPI_ackmessage
    coinIn noteIn UPI NFC_OK'
NFC details
    nfc
NFC mqtt
    nfc_sack
coin machine mqtt
    coin
    note
upi mqtt
    UPI


For a machine;

1 topic will be subscribed by machine - tms1
    Application will publish the message
        Based on the message; product will be dispatched
            Message will be published after the payment verification
1 topic will be published by machine - tmp1
    Application will subscribe the message
        Based on the message; stock availability of the machine will be updated in database
            Message will be published in an interval - (Need to check ?)

No       Description         Application  MQTTBroker  Machine   Expectation
1   All 3 components are up       1           1          1      Works fine
2   Application is down           0           1          1      Application will not work
3   MQTTBroker is down            1           0          1      tms1 message publication will fail
                                                                tmp1 message publication still works but no subscription
                                                                Check before payment if MQTT is up
4   Machine is down               1           1          0      Check before payment if Machine is up
                                                                Check on QR scan if Machine is up
5   App & Broker down             0           0          1      Application will not work
6   App & Machine down            0           1          0      As machine wont work, user can't use it
                                                                Check before payment if Machine is up
7   Broker & Machine down         1           0          0      Application will not work
8   All 3 components are down     0           0          0      Nothing will work


Todo from code side: 

1. Check before payment if Machine/MQTT is up
2. Check if the url is getting opened from QR scan