mosquitto_pub -h mqtt.dotconn.net -t "gtw_0193495b_60bc_781a_8186_da6f0bebf4ad/gen_0193495b_60cb_700a_8c51_595c7ae8c644/subscribe" -m "{\"command\":\"ack\",\"device_id\":\"tng_0193495b_60d2_7638_bf54_5a553161d63f\",\"dispatch\":\"true\",\"payment\":\"s
uccess\"}" -u 0f91c8c8 -P e315d47d

mosquitto_pub -h mqtt.dotconn.net -t "gtw_0193495b_60bc_781a_8186_da6f0bebf4ad/gen_0193495b_60cb_700a_8c51_595c7ae8c644/subscribe" -m "{\"command\":\"nfc_sack\",\"device_id\":\"tng_0193495b_60d2_7638_bf54_5a553161d63f\",\"dispatch\":\"true\"}" -u 0f91c8c8 -P e315d47d

mosquitto_pub -h mqtt.dotconn.net -t "gtw_0193495b_60bc_781a_8186_da6f0bebf4ad/gen_0193495b_60cb_700a_8c51_595c7ae8c644/subscribe" -m "{\"command\":\"getcount\",\"device_id\":\"tng_0193495b_60d2_7638_bf54_5a553161d63f\"}" -u 0f91c8c8 -P e315d47d

