void NFC_DETAILS()
{
  String time = modem.getGSMDateTime(DATE_FULL);
  StaticJsonBuffer<300> JSONbuffer;
  JsonObject &JSONencoder = JSONbuffer.createObject();
  JSONencoder["command"] = "nfc";
  JSONencoder["device_id"] = ID;
  JSONencoder["Date"] = time;
  JSONencoder["data"] = UID;
  JSONencoder["product_available"] = count;
  char JSONmessageBuffer[200];
  JSONencoder.printTo(JSONmessageBuffer, sizeof(JSONmessageBuffer));
  Serial.println("Sending message to MQTT topic..");
  Serial.println(JSONmessageBuffer);
  mqtt.publish(topic1, JSONmessageBuffer);
}

void NFC_mqtt()
{
  String time = modem.getGSMDateTime(DATE_FULL);
  StaticJsonBuffer<300> JSONbuffer;
  JsonObject &JSONencoder = JSONbuffer.createObject();
  JSONencoder["command"] = "nfc_sack";
  JSONencoder["device_id"] = ID;
  JSONencoder["Date"] = time;
  JSONencoder["dispatch"] = "true";
  JSONencoder["product_available"] = count;
  char JSONmessageBuffer[200];
  JSONencoder.printTo(JSONmessageBuffer, sizeof(JSONmessageBuffer));
  Serial.println("Sending message to MQTT topic..");
  Serial.println(JSONmessageBuffer);
  bool ack = mqtt.publish(topic1, JSONmessageBuffer);

  Serial.println(ack);
  // mqtt.publish(topic1, JSONmessageBuffer);
}

void coinmachine_mqtt()
{
  String time = modem.getGSMDateTime(DATE_FULL);
  StaticJsonBuffer<300> JSONbuffer;
  JsonObject &JSONencoder = JSONbuffer.createObject();
  if (coin_mqtt_flag == true)
    JSONencoder["command"] = "coin";
  if (note_mqtt_flag == true)
    JSONencoder["command"] = "note";

  JSONencoder["device_id"] = ID;
  JSONencoder["Date"] = time;
  JSONencoder["product_available"] = count;
  char JSONmessageBuffer[200];
  JSONencoder.printTo(JSONmessageBuffer, sizeof(JSONmessageBuffer));
  Serial.println("Sending message to MQTT topic..");
  Serial.println(JSONmessageBuffer);

  mqtt.publish(topic1, JSONmessageBuffer);
}

void upi_mqtt()
{
  String time = modem.getGSMDateTime(DATE_FULL);
  StaticJsonBuffer<300> JSONbuffer;
  JsonObject &JSONencoder = JSONbuffer.createObject();
  JSONencoder["command"] = "UPI";
  JSONencoder["device_id"] = ID;
  JSONencoder["Date"] = time;
  JSONencoder["Dispatch"] = "true";
  JSONencoder["product_available"] = count;
  char JSONmessageBuffer[200];
  JSONencoder.printTo(JSONmessageBuffer, sizeof(JSONmessageBuffer));
  Serial.println("Sending message to MQTT topic..");
  Serial.println(JSONmessageBuffer);

  mqtt.publish(topic1, JSONmessageBuffer);
}

void getid()
{
  String time = modem.getGSMDateTime(DATE_FULL);
  StaticJsonBuffer<200> JSONbuffer;
  JsonObject &JSONencoder = JSONbuffer.createObject();
  JSONencoder["command"] = "getid";
  JSONencoder["Date"] = time;
  char JSONmessageBuffer[100];
  JSONencoder.printTo(JSONmessageBuffer, sizeof(JSONmessageBuffer));
  Serial.println("Sending message to MQTT topic..");
  Serial.println(JSONmessageBuffer);
  bool ack = mqtt.publish(topic1, JSONmessageBuffer);
  // mqtt.publish(topic1, JSONmessageBuffer);
  Serial.println(ack);
}
