from apscheduler.triggers.cron import Cron<PERSON>rigger
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from src.service.vendor_management_service import VendorManagementService
import pytz
import asyncio

class VendorManagementScheduler:
    """This class acts as a Vendor Management scheduler."""

    async def schedule(self, vm_service_obj: VendorManagementService):
        """This method is to schedule runs."""
        monthly_scheduler = AsyncIOScheduler()
        ist = pytz.timezone("Asia/Kolkata")
        cron_trigger = {
            "hour": 0,
            "minute": 0,
            "day": 1,  # Runs on the 1st day of every month,
            "timezone": ist
        }
        cron_trigger_instance = CronTrigger(cron_trigger)
        monthly_scheduler.add_job(
            vm_service_obj.reset_nfc,
            cron_trigger_instance,
            kwargs={}
        )
        monthly_scheduler.start()
        await asyncio.sleep(1)