import mysql.connector
from src.model.vendor_management_alchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, User, Role, UserRole
from sqlalchemy.orm import joinedload


class SQLUtil:
    """This class acts as a SQL Util."""

    def __init__(self, config_dict):
        """This is a init function to initialize."""
        try:
            alchemy_util = AlchemyUtil(config_dict)
            self.session_local = alchemy_util.session_local
            self.connection = alchemy_util.connection
            print("Database connection is success.")
        except Exception as exc:
            print("Database connection is failed.")
            print(str(exc))

    async def execute_one(self, statement):
        """This function is to execute the select
        query and fetch one result."""
        session = self.session_local()
        try:
            result = session.execute(statement).first()
            if result is None:
                raise Exception("Data not found.")
            return result
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            session.close()

    async def execute_one_with_one(self, statement):
        """This function is to execute the select
        query and fetch one result."""
        session = self.session_local()
        try:
            result = session.execute(statement).scalars().first()
            if result is None:
                raise Exception("Data not found.")
            return result
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            session.close()

    async def execute_all(self, statement):
        """This function is to execute the select
        query and fetch all result."""
        session = self.session_local()
        try:
            result = session.execute(statement).all()
            return result
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            session.close()

    async def execute_all_with_one(self, statement):
        """This function is to execute the select
        query and fetch all result."""
        session = self.session_local()
        try:
            result = session.execute(statement).scalars().all()
            return result
        except Exception as exc:
            print(str(exc))
            raise exc
        finally:
            session.close()

    async def insert(self, model):
        """This function is to execute the insert
        query."""
        session = self.session_local()
        try:
            session.add(model)
            session.commit()
            session.refresh(model)
            return model
        except Exception as exc:
            print(str(exc))
            session.rollback()
            raise exc
        finally:
            session.close()
            

    async def update(self, statement):
        """This function is to execute the insert
        query."""
        session = self.session_local()
        try:
            session.execute(statement)
            session.commit()
        except Exception as exc:
            print(str(exc))
            session.rollback()
            raise exc
        finally:
            session.close()

    async def delete(self, model):
        """This function is to execute the delete
        query."""
        session = self.session_local()
        try:
            session.delete(model)
            session.commit()
        except Exception as exc:
            print(str(exc))
            session.rollback()
            raise exc
        finally:
            session.close()

    async def execute_cud(self, query, params):
        """This function is to insert a new record."""
        cursor = self.conn.cursor(dictionary=True)
        try:
            cursor.execute(query, params)
            self.conn.commit()
        except Exception as exc:
            self.conn.rollback()
            print(str(exc))
            raise exc
        finally:
            cursor.close()

    async def execute_bulk_create(self, insert_query, data_to_insert):
        #connection = self.session_local().get_bind()
        cursor = self.connection.cursor()
        try:
            cursor.executemany(insert_query, data_to_insert)
            self.connection.commit()
        except Exception as exc:
            self.connection.rollback()
            print(str(exc))
            raise exc
        finally:
            cursor.close()



