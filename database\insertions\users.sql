INSERT INTO users (username, password, firstname, lastname, email_id, created_at, updated_at) 
VALUES ('tamiladal', '$2b$12$bKz3sczVGKfjMWycEXv9wOEODnjC3b5lbfgWVnnzYzOpdEVy4xlGa', '<PERSON><PERSON><PERSON><PERSON>', 'V N', '<EMAIL>', NOW(), NOW());
INSERT INTO user_roles (username, role_id, created_at, updated_at)
        SELECT u.username, r.role_id, NOW(), NOW()
        FROM users u, roles r
        WHERE u.email_id = '<EMAIL>' and r.role_name = 'super_admin';
INSERT INTO users (username, password, firstname, lastname, email_id, created_at, updated_at) 
VALUES ('karurkannan', '$2b$12$bKz3sczVGKfjMWycEXv9wOEODnjC3b5lbfgWVnnzYzOpdEVy4xlGa', 'Kannan', '<PERSON><PERSON><PERSON>', '<EMAIL>', NOW(), NOW());
INSERT INTO user_roles (username, role_id, created_at, updated_at)
        SELECT u.username, r.role_id, NOW(), NOW()
        FROM users u, roles r
        WHERE u.email_id = '<EMAIL>' and r.role_name = 'super_admin'