from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from fastapi.responses import JSONResponse
import jwt


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, secret_key):
        super().__init__(app)
        self.secret_key = secret_key

    async def dispatch(self, request: Request, call_next):
        try:
            exceptional_paths = ["/login", "/independent/purchase"]
            exceptional_path_check = request.url.path not in exceptional_paths
            auth_check = "Authorization" in request.headers
            if exceptional_path_check and auth_check:
                token = request.headers["Authorization"]
                # Token is a bearer token
                if "Bearer" not in token:
                    raise Exception("Token is not followed industry standards.")
                formatted_token = token.split("Bearer ")[1]
                try:
                    # Decode the token
                    payload = jwt.decode(formatted_token, self.secret_key, algorithms=["HS256"])
                    request.state.role = payload["role"]
                except jwt.ExpiredSignatureError as err:
                    raise err
                except jwt.InvalidTokenError as err:
                    raise err
        except Exception as exc:
            print(str(exc))
            return JSONResponse(
                status_code=401, content={"error":"Not Authenticated or Session Expired."}
            )
        response = await call_next(request)
        return response
