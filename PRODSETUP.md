ssh root@82.112.226.131 - gWBi68wFby'QeeAr-3)2
cd ..
tmux ls
	vendor-management-service
	source ~/vendor-management-service/venv/bin/activate
which mosquito
	

Start a tmux session - tmux new -s vendor-management-service
Run your Python app - python app.py
Detach with Ctrl+B followed by <PERSON>
Reattach later with - tmux attach -t vendor-management-service

## Fast API Https Setup

sudo apt update
sudo apt install certbot

sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

This will generate an SSL certificate in /etc/letsencrypt/live/yourdomain.com/

git pull https://tamiladal:<EMAIL>/tamiladal/vendor-management-service.git
