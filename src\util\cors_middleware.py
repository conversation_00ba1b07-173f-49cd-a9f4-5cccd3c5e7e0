from fastapi.middleware.cors import CORSMiddleware


class DynamicCORSMiddleware(CORSMiddleware):
    """This class acts as an DynamicCORSMiddleware."""

    def __init__(self, app):
        """This is a init function to initialize."""
        super().__init__(
            app,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
