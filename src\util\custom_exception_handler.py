import traceback

class CustomExceptionHandler:
    """This class is to form custom exception."""

    def handle_exception(self, exc: Exception):
        return {
            "error": str(exc),
            "type": type(exc).__name__,
            "traceback": traceback.format_exc()  # Optional: include the traceback
        }

    def handle_warning(self, warn: RuntimeWarning):
        return {
            "error": "RuntimeWarning",
            "message": str(warn),
            "type": type(warn).__name__,
        }

    def handle_error(self, err: RuntimeError):
        return {
            "error": str(err),
            "type": type(err).__name__,
            "traceback": traceback.format_exc()  # Optional: include the traceback
        }
