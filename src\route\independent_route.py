from src.service.vendor_management_service import VendorManagementService
from fastapi import APIRouter


class IndependentRoute:
    """This class acts as an route for login."""

    def __init__(self, vm_service_obj: VendorManagementService, limiter):
        """This is a init function to initialize."""
        self.vm_service_obj = vm_service_obj
        self.limiter = limiter
        self.router = APIRouter()

    async def load_routes(self):
        rate_limit = "3/minute"
        self.router.add_api_route(
            "/login",
            self.vm_service_obj.validate_user_gen_token,
            methods=["POST"],
            tags=["Login"],
        )
        self.router.add_api_route(
            "/independent/feedback",
            self.limiter.limit(rate_limit)(self.vm_service_obj.create_purchase),
            methods=["POST"],
            tags=["Feedback"],
        )
        self.router.add_api_route(
            "/independent/publish",
            self.limiter.limit(rate_limit)(self.vm_service_obj.publish),
            methods=["POST"],
            tags=["Publish"],
        )
        self.router.add_api_route(
            "/independent/order",
            self.limiter.limit(rate_limit)(self.vm_service_obj.create_order),
            methods=["POST"],
            tags=["Order"],
        )
        self.router.add_api_route(
            "/independent/order",
            self.limiter.limit(rate_limit)(self.vm_service_obj.update_order),
            methods=["PUT"],
            tags=["Order"],
        )
        self.router.add_api_route(
            "/independent/product/{machine_id}",
            self.limiter.limit(rate_limit)(self.vm_service_obj.get_product_by_machine),
            methods=["GET"],
            tags=["Product"]
        )
        self.router.add_api_route(
            "/independent/machine/activeness/{machine_id}",
            self.limiter.limit(rate_limit)(self.vm_service_obj.get_machine_activeness),
            methods=["GET"],
            tags=["Machine"]
        )
        return self.router
