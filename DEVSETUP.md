# Dev Setup

Step 1: Ensure that Python is installed in your system. 

Step 2: Ensure that 'virtualenv' package is installed in your system.

	If not, use the following command; 

		pip install virtualenv

Step 3: Open the project terminal and create a virtual environment using the following command; 

		virtualenv venv
		python -m venv venv

	A virtual environment named 'venv' will be created inside the project. 

Step 4: Now, move to the virtual environment using the following command;

		venv/Scripts/activate

	Successfully the virtual environment is activated.

Step 5: To download and install the required dependencies on the virtual environment, follow the below command

		pip install -r requirements.txt

	Now all the dependencies will be downloaded into the virtual environment.

Note: To run the tests 
		pytest -v
        pytest -v --cov-report term --cov-report xml:coverage.xml --cov=.
		pytest -v --cov-report term --cov-report html --cov-report xml --cov=.
		pytest test/../xyz.py

Step 6: Execute the below command to start the server

		python server.py
		uvicorn app:ptx_app --reload --host 0.0.0.0 --port 7331

	Now the application will be hosted on the port 7331. 

## PEP8 Standards check
flake8 src/ app.py

## PEP8 issues fix
black src/ app.py