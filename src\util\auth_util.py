from fastapi import Request, HTTPException

class PermissionCheck:
    """This class is to handle Authorization."""

    expected_role: str

    def __init__(self, role: str):
        self.expected_role = role

    def __call__(self, request: Request):
        user_role = request.state.role
        print(user_role)
        print(self.expected_role)
        if user_role not in self.expected_role:
            raise HTTPException(status_code=403, detail={"error":"Not Authorized."})
