from pydoc import text
import jwt
import yaml
from sqlalchemy.sql import func, text
from sqlalchemy.sql.expression import cast
from sqlalchemy.types import Interval, String
from sqlalchemy import select, update, and_, desc
from src.util.db_util import DBUtil
from src.util.sql_util import SQLUtil
from src.util.vendor_management_util import VendorManagementUtil
from src.util.razorpay_util import RazorPayUtil
from src.model.vendor_management_alchemy import (
    AlchemyUtil,
    User,
    Role,
    UserRole,
    Machine,
    MachineVendor,
    Stock,
    Product,
    Purchase,
    MqttLogStatus,
    MqttLog,
    Transaction,
    Vendor,
    Nfc
)
from src.model.vendor_management_pydantic import (
    CreateUser,
    CreateMachine,
    UpdateMachine,
    UserRolePy,
    UpdateUser,
    PydanticUser,
    PydanticUserRole,
    PydanticRole,
    DetailedUserRole,
    DetailedUserRoleWithVendor,
    DetailedMachineVendor,
    DetailedMachineVendorAndStock,
    PydanticMachineVendor,
    PydanticProduct,
    PydanticStock,
    DetailedStock,
    CreateStock,
    UpdateStock,
    CreatePurchase,
    DetailedPurchase,
    PydanticPurchase,
    PydanticMachine,
    PydanticMqttLog,
    DetailedMqttLog,
    CreateVendor,
    PydanticVendor,
    DetailedVendor,
    CreateNfc,
    DetailedTransaction,
    PydanticTransaction,
    DetailedProduct,
    CreateProduct,
    DetailedNfc,
    PydanticNfc,
    SalesReportParams,
    PaymentReportParams,
    DashboardParams,
)
import base64
import json
from datetime import datetime, timezone, timedelta
from src.util.mqtt_util import add_mqtt_user, update_acl_file, reload_mqtt_broker
import random
from typing import Dict, Any
import pandas as pd
from io import BytesIO


class VendorManagementConnector:
    """This class acts as an connector for Vendor Management."""

    def __init__(self, secret_key):
        """This is a init function to initialize."""
        self.secret_key = secret_key
        config_path = "../vendor-management-service/src/util/config.yml"
        with open(config_path, "r") as file:
            self.config_dict = yaml.safe_load(file)
        self.alchemy_util_obj = SQLUtil(self.config_dict)
        self.vm_util_obj = VendorManagementUtil()
        self.razorpay_util_obj = RazorPayUtil(self.config_dict)
        self.product_availabilities = {}

    async def validate_user(self, username, password):
        """This function is to validate the logged in user."""
        stmt = (
            select(User, UserRole, Role, Vendor)
            .join(UserRole, User.username == UserRole.username)
            .join(Role, UserRole.role_id == Role.role_id)
            .outerjoin(Vendor, User.vendor_id == Vendor.vendor_id)
            .where(User.username == username)
        )
        result = await self.alchemy_util_obj.execute_one(stmt)
        if result:
            user, user_role, role, vendor = result
            if user.verify_password(password):
                combined_result = await DetailedUserRoleWithVendor(
                    user=PydanticUser.from_orm(user),
                    user_role=(
                        PydanticUserRole.from_orm(user_role) if user_role else None
                    ),
                    role=PydanticRole.from_orm(role) if role else None,
                    vendor=PydanticVendor.from_orm(vendor) if vendor else None,
                ).read()
                return combined_result
            else:
                return None
        else:
            return None

    async def generate_token(self, user):
        """This function is to generate JWT token."""
        payload = {
            "username": user["username"],
            "firstname": user["firstname"],
            "lastname": user["lastname"],
            "role": user["role"],
            "vendor_id": user["vendor_id"]
        }
        return jwt.encode(
            payload, self.secret_key, algorithm=self.config_dict["jwt_algorithm"]
        )

    async def create_vendor(self, vendor: CreateVendor):
        """This function is to create new vendor."""
        try:
            vendor_model = Vendor()
            random_value = random.randint(0000, 9999)
            vendor_model.vendor_id = f"vendor_{random_value}"
            vendor_model.company_name = vendor.company_name
            vendor_model.gst = vendor.gst
            vendor_model.address_1 = vendor.address_1
            vendor_model.address_2 = vendor.address_2
            vendor_model.city = vendor.city
            vendor_model.state = vendor.state
            vendor_model.pincode = vendor.pincode
            vendor_model.phone_number = vendor.phone_number
            vendor_model.email_id = vendor.email_id
            vendor_model.is_active = True
            await self.alchemy_util_obj.insert(vendor_model)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_vendor(self, vendor_dict: Dict[str, Any]):
        """This function is to update the vendor."""
        try:
            if "vendor_id" not in vendor_dict:
                raise Exception("Vendor id not found.")
            vendor_id = vendor_dict["vendor_id"]
            update_stmt = (
                update(Vendor)
                .where(Vendor.vendor_id == vendor_id)
                .values(
                    **vendor_dict
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_vendors(self):
        """This function is to get the vendors."""
        try:
            stmt = select(Vendor)
            results = await self.alchemy_util_obj.execute_all_with_one(stmt)
            combined_results = []
            for result in results:
                combined_result = await DetailedVendor(
                    vendor=PydanticVendor.from_orm(result)
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_vendor_by_id(self, vendor_id):
        """This function is to get vendor by id."""
        try:
            stmt = select(Vendor).where(Vendor.vendor_id == vendor_id)
            result = await self.alchemy_util_obj.execute_one_with_one(stmt)
            if result:
                combined_result = await DetailedVendor(
                    vendor=PydanticVendor.from_orm(result)
                ).read()
                return combined_result
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_vendor(self, vendor_id):
        """This function is to delete vendor."""
        try:
            update_stmt = (
                update(Vendor)
                .where(Vendor.vendor_id == vendor_id)
                .values(is_active=False)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc   

    async def get_users(self):
        """This function is to get all users."""
        try:
            stmt = (
                select(User, UserRole, Role, Vendor)
                .join(UserRole, User.username == UserRole.username)
                .join(Role, UserRole.role_id == Role.role_id)
                .outerjoin(Vendor, User.vendor_id == Vendor.vendor_id)
            )
            results = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for user, user_role, role, vendor in results:
                combined_result = await DetailedUserRoleWithVendor(
                    user=PydanticUser.from_orm(user),
                    user_role=(
                        PydanticUserRole.from_orm(user_role) if user_role else None
                    ),
                    role=PydanticRole.from_orm(role) if role else None,
                    vendor=PydanticVendor.from_orm(vendor) if vendor else None,
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_user(self, username: str):
        """This function is to get the user."""
        try:
            stmt = (
                select(User, UserRole, Role)
                .join(UserRole, User.username == UserRole.username)
                .join(Role, UserRole.role_id == Role.role_id)
                .where(User.username == username)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                user, user_role, role = result
                combined_result = await DetailedUserRole(
                    user=PydanticUser.from_orm(user),
                    user_role=PydanticUserRole.from_orm(user_role) if user_role else None,
                    role=PydanticRole.from_orm(role) if role else None
                ).read()
                return combined_result
            else:
                return None
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_user(self, user: CreateUser):
        """This function is to create the user."""
        try:
            user_model = User()
            user_model.username = user.username
            user_model.set_password(user.password)
            user_model.firstname = user.firstname
            user_model.lastname = user.lastname
            user_model.email_id = user.email_id
            user_model.phone_number = user.phone_number
            user_model.vendor_id = user.vendor_id
            user_role_model = UserRole()
            user_role_model.user = user_model
            r_stmt = select(Role).where(Role.role_id == user.role_id)
            result = await self.alchemy_util_obj.execute_one_with_one(r_stmt)
            if result:
                user_role_model.role = result
            await self.alchemy_util_obj.insert(user_role_model)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_user(self, user_dict: Dict[str, Any]):
        """This function is to update the user."""
        try:
            if "username" not in user_dict:
                raise Exception("Username not found.")
            username = vendor_dict["username"]
            update_stmt = (
                update(User)
                .where(User.username == username)
                .values(
                    **vendor_dict
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_user_role(self, user: UserRolePy):
        """This function is to update the user role."""
        try:
            update_stmt = (
                update(UserRole)
                .where(UserRole.username == user.username)
                .values(role_id=user.role)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_user(self, username: str):
        """This function is to soft delete the user."""
        try:
            update_stmt = (
                update(User)
                .where(User.username == username)
                .values(is_active=False)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_machine_subscribed_topic(self, machine_id):
        """This fucntion is to get product count."""
        try:
            stmt = (
                select(MachineVendor, Machine).join(Machine, Machine.machine_id == MachineVendor.machine_id).where(MachineVendor.machine_id == machine_id)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                machineuser, machine = result
                combined_result = await DetailedMachineVendor(
                    machine_vendor=PydanticMachineVendor.from_orm(machineuser),
                    machine=PydanticMachine.from_orm(machine),
                ).read()
                return combined_result
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_machines(self):
        """This function is to get all machines."""
        try:
            stmt = select(MachineVendor, Machine, Stock, Product, Vendor).join(Machine, Machine.machine_id == MachineVendor.machine_id).join(Stock, Stock.machine_vendor_id == MachineVendor.machine_vendor_id).join(Product, Product.product_id == Stock.product_id).join(Vendor, Vendor.vendor_id == MachineVendor.vendor_id)
            results = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for machine_vendor, machine, stock, product, vendor in results:
                combined_result = await DetailedMachineVendorAndStock(
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor),
                    machine=PydanticMachine.from_orm(machine),
                    stock=PydanticStock.from_orm(stock),
                    product=PydanticProduct.from_orm(product),
                    vendor=PydanticVendor.from_orm(vendor)
                ).read()
                try:
                    trans_stmt = select(Transaction).where(Transaction.machine_id == combined_result["machine_id"], Transaction.status == "Completed").order_by(desc(Transaction.updated_at)).limit(1)
                    trans_result = await self.alchemy_util_obj.execute_one_with_one(trans_stmt)
                except Exception as exc:
                    trans_result = None
                if trans_result:
                    combined_result["last_order_fetched_at"] = trans_result.updated_at.isoformat()
                else:
                    combined_result["last_order_fetched_at"] = "UNKNOWN"
                if combined_result["qr_code"] is not None:
                    combined_result["qr_code"] = base64.b64encode(
                        combined_result["qr_code"]
                    ).decode()
                if combined_result["product_image"] is not None:
                    combined_result["product_image"] = base64.b64encode(
                            combined_result["product_image"]
                    ).decode()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_machines_by(self, machine_details):
        """This function is to get all machines."""
        try:
            combined_results = []
            if "machine_id" not in machine_details and "vendor_id" not in machine_details:
                raise Exception("Both filters are not provided.")
            elif "machine_id" in machine_details and machine_details["machine_id"]:
                machine_id = machine_details["machine_id"]
                stmt = (
                    select(MachineVendor, Machine, Stock, Product, Vendor).join(Machine, Machine.machine_id == MachineVendor.machine_id).join(Stock, Stock.machine_vendor_id == MachineVendor.machine_vendor_id).join(Product, Product.product_id == Stock.product_id).outerjoin(Vendor, Vendor.vendor_id == MachineVendor.vendor_id).where(MachineVendor.machine_id == machine_id)
                )
                result = await self.alchemy_util_obj.execute_one(stmt)
                if result:
                    machineuser, machine, stock, product, vendor  = result
                    combined_result = await DetailedMachineVendorAndStock(
                        machine_vendor=PydanticMachineVendor.from_orm(machineuser),
                        machine=PydanticMachine.from_orm(machine),
                        stock=PydanticStock.from_orm(stock),
                        product=PydanticProduct.from_orm(product),
                        vendor=PydanticVendor.from_orm(vendor)
                    ).read()
                    try:
                        trans_stmt = select(Transaction).where(Transaction.machine_id == combined_result["machine_id"], Transaction.status == "Completed").order_by(desc(Transaction.updated_at)).limit(1)
                        trans_result = await self.alchemy_util_obj.execute_one_with_one(trans_stmt)
                    except Exception as exc:
                        trans_result = None
                    if trans_result:
                        combined_result["last_order_fetched_at"] = trans_result.updated_at.isoformat()
                    else:
                        combined_result["last_order_fetched_at"] = "UNKNOWN"
                    if combined_result["qr_code"] is not None:
                        combined_result["qr_code"] = base64.b64encode(
                            combined_result["qr_code"]
                        ).decode()
                    if combined_result["product_image"] is not None:
                        combined_result["product_image"] = base64.b64encode(
                            combined_result["product_image"]
                        ).decode()
                    combined_results.append(combined_result)
                return combined_results
            elif "vendor_id" in machine_details and machine_details["vendor_id"]:
                vendor_id = machine_details["vendor_id"]
                if vendor_id == "All":
                    stmt = (
                        select(MachineVendor, Machine, Stock, Product, Vendor).join(Machine, Machine.machine_id == MachineVendor.machine_id).join(Stock, Stock.machine_vendor_id == MachineVendor.machine_vendor_id).join(Product, Product.product_id == Stock.product_id).join(Vendor, Vendor.vendor_id == MachineVendor.vendor_id)
                    )
                else:
                    stmt = (
                        select(MachineVendor, Machine, Stock, Product, Vendor).join(Machine, Machine.machine_id == MachineVendor.machine_id).join(Stock, Stock.machine_vendor_id == MachineVendor.machine_vendor_id).join(Product, Product.product_id == Stock.product_id).join(Vendor, Vendor.vendor_id == MachineVendor.vendor_id).where(MachineVendor.vendor_id == vendor_id)
                    )
                results = await self.alchemy_util_obj.execute_all(stmt)
                for machineuser, machine, stock, product, vendor in results: 
                    combined_result = await DetailedMachineVendorAndStock(
                        machine_vendor=PydanticMachineVendor.from_orm(machineuser),
                        machine=PydanticMachine.from_orm(machine),
                        stock=PydanticStock.from_orm(stock),
                        product=PydanticProduct.from_orm(product),
                        vendor=PydanticVendor.from_orm(vendor)
                    ).read()
                    try:
                        trans_stmt = select(Transaction).where(Transaction.machine_id == combined_result["machine_id"], Transaction.status == "Completed").order_by(desc(Transaction.updated_at)).limit(1)
                        trans_result = await self.alchemy_util_obj.execute_one_with_one(trans_stmt)
                    except Exception as exc:
                        trans_result = None
                    if trans_result:
                        combined_result["last_order_fetched_at"] = trans_result.updated_at.isoformat()
                    else:
                        combined_result["last_order_fetched_at"] = "UNKNOWN"
                    if combined_result["qr_code"] is not None:
                        combined_result["qr_code"] = base64.b64encode(
                            combined_result["qr_code"]
                        ).decode()
                    if combined_result["product_image"] is not None:
                        combined_result["product_image"] =  base64.b64encode(
                            combined_result["product_image"]
                        ).decode()
                    combined_results.append(combined_result)
                return combined_results
            return None
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_qr_code(self, machine_id: str):
        """This function is to get QR code."""
        try:
            stmt = (
                select(MachineVendor, Machine).join(Machine, Machine.machine_id == MachineVendor.machine_id).where(MachineVendor.machine_id == machine_id)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                machineuser, machine = result
                combined_result = await DetailedMachineVendor(
                    machine_vendor=PydanticMachineVendor.from_orm(machineuser),
                    machine=PydanticMachine.from_orm(machine),
                ).read()
                return combined_result["qr_code"]
            else:
                raise Exception("Data not found.")
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_machine(self, machine_dict: Dict[str, Any]):
        """This function is to update the nfc."""
        try:
            if "machine_id" not in machine_dict:
                raise Exception("Machine id not found.")
            machine_id = machine_dict["machine_id"]
            # Get column names from the model
            machine_columns = set(c.name for c in Machine.__table__.columns)

            # Filter the dict
            filtered_dict = {k: v for k, v in machine_dict.items() if k in machine_columns}
            update_stmt = (
                update(Machine)
                .where(Machine.machine_id == machine_id)
                .values(
                    **filtered_dict
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_machine(self, machine: CreateMachine):
        """This function is to create the machine."""
        try:
            machine_model = Machine()
            machine_model.purchase_date = machine.purchase_date
            machine_model.expiry_date = machine.expiry_date
            machine_model.model_number = machine.model_number
            machine_model.serial_number = machine.serial_number
            machine_model.imei_number = machine.imei_number
            machine_model.sim_number = machine.sim_number
            machine_model.latitude = machine.latitude
            machine_model.longitude = machine.longitude
            machine_model.warranty = machine.warranty
            machine_vendor_model = MachineVendor()
            machine_vendor_model.vendor_id = machine.vendor_id
            machine_vendor_model.machine = machine_model

            #machine_vendor_model.razorpay_key = machine.razorpay_key
            #machine_vendor_model.razorpay_secret = machine.razorpay_secret
            machine_vendor_model.device_id = machine.device_id
            machine_vendor_model.topic_to_publish = f"vms/{machine.serial_number}_{machine.vendor_id}_pub"
            topic_to_subscribe = f"vms/{machine.serial_number}_{machine.vendor_id}_sub"
            machine_vendor_model.topic_to_subscribe = topic_to_subscribe

            password_file = self.config_dict["password_file"]
            print(f"{machine.mqtt_username} : {machine.mqtt_password}")
            await add_mqtt_user(machine.mqtt_username, machine.mqtt_password, password_file)
            topics = {
                machine_vendor_model.topic_to_publish: "readwrite",
                machine_vendor_model.topic_to_subscribe: "read"
            }
            acl_file = self.config_dict["acl_file"]
            await update_acl_file(machine.mqtt_username, topics, acl_file)
            machine_vendor_model.mqtt_username = machine.mqtt_username
            machine_vendor_model.set_mqtt_password(machine.mqtt_password)

            created_machine_vendor = await self.alchemy_util_obj.insert(machine_vendor_model)
            cmv_pydantic = PydanticMachineVendor.from_orm(created_machine_vendor)
            machine_id = cmv_pydantic.machine_id
            await self.regenerate_qr_code(machine_id)
            broker = self.config_dict["broker"]
            mqtt_username = self.config_dict["mqtt_username"]
            mqtt_password = self.config_dict["mqtt_password"]
            await reload_mqtt_broker(broker, mqtt_username, mqtt_password, machine.mqtt_username)
            machine_detail = {
                "machine_id": machine_id,
                "topic_to_subscribe": topic_to_subscribe
            }
            return machine_detail    
        except Exception as exc:
            print(str(exc))
            raise exc

    async def regenerate_qr_code(self, machine_id: str):
        """This function is to update the machine."""
        try:
            qr_code_bytes = await self.vm_util_obj.generate_qr_code(machine_id)
            update_stmt = (
                update(MachineVendor)
                .where(MachineVendor.machine_id == machine_id)
                .values(qr_code=qr_code_bytes)
            )
            await self.alchemy_util_obj.update(update_stmt)
            return qr_code_bytes
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_machine(self, machine_id: int):
        """This function is to delete the machine."""
        try:
            update_stmt = (
                update(Machine)
                .where(Machine.machine_id == machine_id)
                .values(is_active=False)
            )
            await self.alchemy_util_obj.update(update_stmt)
            if result:
                await self.alchemy_util_obj.delete(result)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_stocks(self):
        """This function is to get all machines."""
        try:
            stmt = (
                select(Stock, MachineVendor, Product)
                .join(MachineVendor, Stock.machine_vendor_id == MachineVendor.machine_vendor_id)
                .join(Product, Stock.product_id == Product.product_id)
            )
            results = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for stock, machine_vendor, product in results:
                combined_result = await DetailedStock(
                    stock=PydanticStock.from_orm(stock),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor),
                    product=PydanticProduct.from_orm(product),
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_stock_by_id(self, stock_id: str):
        """This function is to get a single stock."""
        try:
            stmt = (
                select(Stock, MachineVendor, Product)
                .join(MachineVendor, Stock.machine_vendor_id == MachineVendor.machine_vendor_id)
                .join(Product, Stock.product_id == Product.product_id)
                .where(Stock.stock_id == stock_id)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                stock, machine_vendor, product = result
                combined_result = await DetailedStock(
                    stock=PydanticStock.from_orm(stock),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor),
                    product=PydanticProduct.from_orm(product),
                ).read()
                return combined_result
            return None
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_stock(self, stock: CreateStock):
        """This function is to create the stock."""
        try:
            product_stmt = select(Product).where(
                Product.product_id == stock.product_id
            )
            product = await self.alchemy_util_obj.execute_one_with_one(product_stmt)
            machine_vendor_stmt = select(MachineVendor).where(
                and_(MachineVendor.vendor_id == stock.vendor_id,
                MachineVendor.machine_id == stock.machine_id)
            )
            machine_vendor = await self.alchemy_util_obj.execute_one_with_one(
                machine_vendor_stmt
            )
            if product and machine_vendor:
                stock_model = Stock()
                stock_model.capacity = stock.capacity
                stock_model.availability = stock.availability
                stock_model.product_sold = 0
                stock_model.product_id = product.product_id
                stock_model.product_price = stock.price
                stock_model.products.append(product)
                stock_model.machine_vendor = machine_vendor
                await self.alchemy_util_obj.insert(stock_model)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_stock_availability(self, stock: UpdateStock):
        """This function is to update the stock availability."""
        try:
            update_stmt = (
                update(Stock)
                .where(Stock.stock_id == stock.stock_id)
                .values(availability=stock.availability)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc
        
    async def init_nfc_validation(self, topic: str, payload: dict, mqtt_util_obj):
        """This function is to validate the nfc tag."""
        if payload["command"] != "NFC":
            return
        nfc_id = payload["data"]
        device_id = payload["device_id"]
        await self.validate_nfc(nfc_id, device_id, mqtt_util_obj)

    async def init_nfc_validation(self, topic: str, payload: dict, mqtt_util_obj):
        """This function is to validate the nfc tag."""
        if payload["command"] != "NFC":
            return
        nfc_id = payload["data"]
        device_id = payload["device_id"]
        await self.validate_nfc(nfc_id, device_id, mqtt_util_obj)

    async def update_stock_availability_from_mqtt(self, topic: str, payload: dict):
        """This function is to update the stock availability."""
        try:
            print(payload)
            if payload["command"] in ["nfc_sack", "coin", "note", "UPI", "setcount"]:
                stmt = select(MachineVendor).where(MachineVendor.topic_to_publish == topic)
                result = await self.alchemy_util_obj.execute_one_with_one(stmt)
                if payload["command"] not in ["setcount"]:
                    update_stmt = (
                        update(Stock)
                        .where(Stock.machine_vendor_id == result.machine_vendor_id)
                        .values(availability=Stock.availability - 1)
                    )
                    await self.alchemy_util_obj.update(update_stmt)
                if payload["command"] in ["nfc_sack", "coin", "note"]:
                    transaction = Transaction()
                    transaction.order = "Not applicable"
                    transaction.status = "Completed"
                    transaction.payment_status = "Completed"
                    transaction.payment_type = payload["command"]
                    transaction.transaction_id = random.randint(0000, 9999)
                    transaction.machine_id = result.machine_id
                    await self.alchemy_util_obj.insert(transaction)
                if payload["command"] == "setcount":
                    print("Setting product availability to True for machine_id: ", result.machine_id)
                    self.product_availabilities[str(result.machine_id)] = True
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_stock(self, stock_id: int):
        """This function is to delete the stock."""
        try:
            stmt = select(Stock).where(Stock.stock_id == stock_id)
            result = await self.alchemy_util_obj.execute_one_with_one(stmt)
            if result:
                await self.alchemy_util_obj.delete(result)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_purchases(self):
        """This function is to get all purchases."""
        try:
            stmt = select(Purchase, Stock).join(
                Stock, Purchase.stock_id == Stock.stock_id
            )
            result = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for purchase, stock in result:
                combined_result = await DetailedPurchase(
                    purchase=PydanticPurchase.from_orm(purchase),
                    stock=PydanticStock.from_orm(stock),
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_purchase_by_id(self, purchase_id: str):
        """This function is to get a single purchase."""
        try:
            stmt = (
                select(Purchase, Stock)
                .join(Stock, Purchase.stock_id == Stock.stock_id)
                .where(Purchase.purchase_id == purchase_id)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                purchase, stock = result
                combined_result = await DetailedPurchase(
                    purchase=PydanticPurchase.from_orm(purchase),
                    stock=PydanticStock.from_orm(stock),
                ).read()
                return combined_result
            return None
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_purchase(self, purchase: CreatePurchase):
        """This function is to create the purchase."""
        try:
            stock_stmt = select(Stock).where(Stock.stock_id == Stock.stock_id)
            stock = await self.alchemy_util_obj.execute_one_with_one(stock_stmt)
            if stock:
                if stock.availability > 0:
                    purchase_model = Purchase()
                    purchase_model.stock_id = stock.stock_id
                    purchase_model.feedback = purchase.feedback
                    purchase_model.rating = purchase.rating
                    stock.availability = stock.availability - 1
                    purchase_model.stock = stock

                    await self.alchemy_util_obj.insert(purchase_model)
                else:
                    raise Exception("Stock availability issue.")
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_purchase(self, purchase_id: int):
        """This function is to delete the purchase."""
        try:
            stmt = select(Purchase).where(Purchase.purchase_id == purchase_id)
            result = await self.alchemy_util_obj.execute_one_with_one(stmt)
            if result:
                await self.alchemy_util_obj.delete(result)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_mqtt_log(self, topic_name, payload):
        """This function is to create the mqtt log."""
        try:
            mqtt_log = MqttLog()
            mqtt_log.topic_name = topic_name
            mqtt_log.payload = json.dumps(payload)
            mqtt_log.status = MqttLogStatus.NEW
            return await self.alchemy_util_obj.insert(mqtt_log)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_mqtt_log(self, mqtt_log_id):
        """This function is to get the mqtt log."""
        try:
            stmt = select(MqttLog).where(MqttLog.mqtt_log_id == mqtt_log_id)
            result = await self.alchemy_util_obj.execute_one_with_one(stmt)
            return await DetailedMqttLog(
                    mqttlog=PydanticMqttLog.from_orm(result)
                ).read()
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_new_mqtt_logs(self):
        """This function is to get the mqtt logs."""
        combined_results = []
        try:
            stmt = select(MqttLog).where(MqttLog.status == MqttLogStatus.NEW)
            results = await self.alchemy_util_obj.execute_all_with_one(stmt)
            for result in results:
                combined_result = await DetailedMqttLog(
                    mqttlog=PydanticMqttLog.from_orm(result)
                ).read()
                combined_results.append(combined_result)
        except Exception as exc:
            print(str(exc))
        finally:
            return combined_results

    async def get_mqtt_logs(self):
        """This function is to get mqtt logs."""
        combined_results = []
        try:
            stmt = select(MqttLog)
            results = await self.alchemy_util_obj.execute_all_with_one(stmt)
            for result in results:
                combined_result = await DetailedMqttLog(
                    mqttlog=PydanticMqttLog.from_orm(result)
                ).read()
                combined_results.append(combined_result)
        except Exception as exc:
            print(str(exc))
        finally:
            return combined_results

    async def update_mqtt_log(self, mqtt_log_id, mqtt_log_status):
        """This function is to create the mqtt log."""
        try:
            update_stmt = (
                update(MqttLog)
                .where(MqttLog.mqtt_log_id == mqtt_log_id)
                .values(status = mqtt_log_status)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def complete_mqtt_log(self, mqtt_log_id):
        """This function is to create the mqtt log."""
        try:
            update_stmt = (
                update(MqttLog)
                .where(MqttLog.mqtt_log_id == mqtt_log_id)
                .values(status = MqttLogStatus.COMPLETE, completed_at = datetime.now())
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_topics_to_subscribe(self):
        """This function is to get the topics to be subscribed by API"""
        topics = []
        try:
            stmt = select(MachineVendor)
            results = await self.alchemy_util_obj.execute_all_with_one(stmt)
            for result in results:
                topics.append(result.topic_to_publish)
        except Exception as exc:
            print(str(exc))
        finally:
            return topics

    async def create_order(self, order: dict):
        """This function is to create the order."""
        try:
            if 'amount' not in order or 'machine_id' not in order:
                raise Exception("Order details are missing.")
            else:    
                #
                random_value = random.randint(00000, 99999)
                receipt = f"Receipt_{random_value}"
                machine_id = order["machine_id"]
                stmt = (
                    select(MachineVendor, Machine).join(Machine, Machine.machine_id == MachineVendor.machine_id).where(MachineVendor.machine_id == machine_id)
                )
                result = await self.alchemy_util_obj.execute_one(stmt)
                if result:
                    machinevendor, machine  = result
                    combined_result = await DetailedMachineVendor(
                        machine=PydanticMachine.from_orm(machine),
                        machine_vendor=PydanticMachineVendor.from_orm(machinevendor)
                    ).read()
                    if "razorpay_key" in combined_result and "razorpay_secret" in combined_result:
                        razorpay_key = combined_result["razorpay_key"]
                        razorpay_secret = combined_result["razorpay_secret"]
                        razorpay_client = await self.razorpay_util_obj.get_client(razorpay_key, razorpay_secret)
                    else:
                        razorpay_client = self.razorpay_util_obj.client
                        razorpay_key = self.razorpay_util_obj.key_id
                        razorpay_secret = self.razorpay_util_obj.key_secret
                    order_detail = await self.razorpay_util_obj.create_order(order['amount'], receipt, razorpay_client)
                    if "amount" in order_detail:
                        order_detail["amount"] = order_detail["amount"]/100
                    if "amount_due" in order_detail:
                        order_detail["amount_due"] = order_detail["amount_due"]/100
                    if "amount_paid" in order_detail:
                        order_detail["amount_paid"] = order_detail["amount_paid"]/100
                    transaction = Transaction()
                    transaction.order = json.dumps(order_detail)
                    transaction.status = "Order_Initiated"
                    transaction.payment_status = order_detail['status']
                    transaction.payment_type = "UPI"
                    transaction.transaction_id = order_detail['id']
                    transaction.machine_id = order['machine_id']
                    await self.alchemy_util_obj.insert(transaction)
                    return {
                        "order_id": order_detail['id'],
                        "razorpay_key": razorpay_key
                    }
                else:
                    raise Exception("Machine detail is not found.")
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_order(self, order: dict, mqtt_util_obj):
        """This function is to verify the order."""
        status = "Completed"
        exception_message = ""
        try:
            if 'razorpay_payment_id' not in order or 'order_id' not in order or 'razorpay_signature' not in order:
                raise Exception("Order details are missing.")
            else:
                machine_id = order["machine_id"]
                stmt = (
                    select(MachineVendor, Machine).join(Machine, Machine.machine_id == MachineVendor.machine_id).where(MachineVendor.machine_id == machine_id)
                )
                result = await self.alchemy_util_obj.execute_one(stmt)
                if result:
                    machinevendor, machine  = result
                    combined_result = await DetailedMachineVendor(
                        machine=PydanticMachine.from_orm(machine),
                        machine_vendor=PydanticMachineVendor.from_orm(machinevendor)
                    ).read()
                    if "razorpay_key" in combined_result and "razorpay_secret" in combined_result:
                        razorpay_secret = combined_result["razorpay_secret"]
                    else:
                        razorpay_secret = self.razorpay_util_obj.key_secret
                    signature_verification = await self.razorpay_util_obj.verify_signature(razorpay_secret, order['razorpay_payment_id'], order['order_id'], order['razorpay_signature'])
                    if(signature_verification is False):
                        status = "Failed"
                        exception_message = "Signature verification is failed."
        except Exception as exc:
            exception_message = str(exc)
            print(exception_message)
            status = "Failed"
        finally:
            if 'razorpay_payment_id' in order and 'order_id' in order and 'razorpay_signature' in order:
                update_stmt = (
                        update(Transaction)
                        .where(Transaction.transaction_id == order['order_id'])
                        .values(
                            payment_status=status,
                            status=status,
                            payment_id=order['razorpay_payment_id']
                        )
                    )
                await self.alchemy_util_obj.update(update_stmt)
            if status == "Completed":
                stmt = select(Transaction).where(Transaction.transaction_id == order['order_id'])
                transaction_result = await self.alchemy_util_obj.execute_one_with_one(stmt)
                stmt = select(MachineVendor).where(MachineVendor.machine_id == transaction_result.machine_id)
                machine_vendor_result = await self.alchemy_util_obj.execute_one_with_one(stmt)
                payload = {
                    "device_id": machine_vendor_result.device_id,
                    "command": "ack",
                    "dispatch": "true",
                    "payment": "success"
                }
                await mqtt_util_obj.publish(machine_vendor_result.topic_to_subscribe, payload)
            else:
                raise Exception(exception_message)

    async def upload_nfc(self, file):
        """This function is to upload nfc."""
        try:
            # Read the uploaded file into a pandas DataFrame
            contents = await file.read()
            df = pd.read_excel(BytesIO(contents))
            #df['purchase_history'] = df['purchase_history'].apply(json.dumps([]))
            df['purchase_history'] = json.dumps([])
            df['is_active'] = True
            # Validate DataFrame structure (optional, depending on your needs)
            expected_columns = ["nfc_id", "name", "frequency_type", "frequency", "usability", "machine_id"]  # Replace with your table's column names
            if not all(col in df.columns for col in expected_columns):
                raise Exception(f"Excel file must contain the following columns: {expected_columns}")

            # Insert DataFrame into MySQL database
            insert_query = """
                INSERT INTO nfcs (nfc_id, name, frequency_type, frequency, usability, machine_id, purchase_history, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, CAST(%s AS JSON), %s)
            """
            data_to_insert = df.values.tolist()
            await self.alchemy_util_obj.execute_bulk_create(insert_query, data_to_insert)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_nfcs(self):
        """This function is to get nfcs."""
        combined_results = []
        try:
            stmt = select(Nfc, MachineVendor).join(MachineVendor, Nfc.machine_id == MachineVendor.machine_id)
            results = await self.alchemy_util_obj.execute_all(stmt)
            for nfc, machine_vendor in results:
                combined_result = await DetailedNfc(
                    nfc=PydanticNfc.from_orm(nfc),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor)
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_nfc(self, nfc_id):
        """This function is to get nfc by id."""
        try:
            stmt = select(Nfc, MachineVendor).join(MachineVendor, Nfc.machine_id == MachineVendor.machine_id).where(Nfc.nfc_id == nfc_id)
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                nfc, machine_vendor = result
                combined_result = await DetailedNfc(
                    nfc=PydanticNfc.from_orm(nfc),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor)
                ).read()
                return combined_result
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_nfc(self, create_nfc: CreateNfc):
        """This function is to create the nfc."""
        try:
            nfc_model = Nfc()
            nfc_model.frequency_type = create_nfc.frequency_type
            nfc_model.frequency = create_nfc.frequency
            nfc_model.name = create_nfc.name
            nfc_model.machine_id = create_nfc.machine_id
            nfc_model.is_active = True
            await self.alchemy_util_obj.insert(nfc_model)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_nfc(self, nfc_dict: Dict[str, Any]):
        """This function is to update the nfc."""
        try:
            if "nfc_id" not in nfc_dict:
                raise Exception("Nfc id not found.")
            nfc_id = nfc_dict["nfc_id"]
            update_stmt = (
                update(Nfc)
                .where(Nfc.nfc_id == nfc_id)
                .values(
                    **nfc_dict
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_nfc(self, nfc_id):
        """This function is to delete nfc."""
        try:
            update_stmt = (
                update(Nfc)
                .where(Nfc.nfc_id == nfc_id)
                .values(is_active=False)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc   

    async def get_orders_by_machine(self, machine_id):
        """This function is to get all orders."""
        try:
            stmt = select(Transaction, MachineVendor, Stock, Product).join(MachineVendor, MachineVendor.machine_id == Transaction.machine_id).join(Stock, Stock.machine_vendor_id == MachineVendor.machine_vendor_id).join(Product, Product.product_id == Stock.product_id).where(Transaction.machine_id == machine_id)
            results = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for transaction, machine_vendor, stock, product in results:
                combined_result = await DetailedTransaction(
                    transaction=PydanticTransaction.from_orm(transaction),
                    product=PydanticProduct.from_orm(product),
                    stock=PydanticStock.from_orm(stock)
                ).read()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_products(self):
        """This function is to get all products."""
        try:
            stmt = select(Product)
            result = await self.alchemy_util_obj.execute_all_with_one(stmt)
            combined_results = []
            for product in result:
                combined_result = await DetailedProduct(
                    product=PydanticProduct.from_orm(product)
                ).read()
                combined_result["image"] = base64.b64encode(
                        combined_result["image"]
                    ).decode()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_product_by_machine(self, machine_id: str):
        """This function is to get all products."""
        try:
            stmt = (
                select(MachineVendor, Stock, Product)
                .join(Stock, MachineVendor.machine_vendor_id == Stock.machine_vendor_id)
                .join(Product, Stock.product_id == Product.product_id)
                .where(MachineVendor.machine_id == machine_id)
            )
            result = await self.alchemy_util_obj.execute_one(stmt)
            if result:
                machine_vendor, stock, product = result
                combined_result = await DetailedStock(
                    product=PydanticProduct.from_orm(product),
                    stock=PydanticStock.from_orm(stock),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor)
                ).read()
                if combined_result["product_image"] is not None:
                    combined_result["product_image"] = base64.b64encode(
                        combined_result["product_image"]
                    ).decode()
                return combined_result
            return None
        except Exception as exc:
            print(str(exc))
            raise exc

    async def get_products_by_vendor(self, vendor_id: str):
        """This function is to get the products by vendor."""
        combined_results = []
        try:
            stmt = (
                select(MachineVendor, Stock, Product)
                .join(Stock, MachineVendor.machine_vendor_id == Stock.machine_vendor_id)
                .join(Product, Stock.product_id == Product.product_id)
                .where(MachineVendor.vendor_id == vendor_id)
            )
            results = await self.alchemy_util_obj.execute_all(stmt)
            for machine_vendor, stock, product in result:
                combined_result = await DetailedStock(
                    product=PydanticProduct.from_orm(product),
                    stock=PydanticStock.from_orm(stock),
                    machine_vendor=PydanticMachineVendor.from_orm(machine_vendor)
                ).read()
                if combined_result["product_image"] is not None:
                    combined_result["product_image"] = base64.b64encode(
                        combined_result["product_image"]
                    ).decode()
                combined_results.append(combined_result)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc

    async def create_product(self, create_product: CreateProduct):
        """This function is to create the nfc."""
        try:
            product_model = Product()
            product_model.product_name = create_product.product_name
            product_model.image = create_product.image
            product_model.specification = create_product.specification
            product_model.size = create_product.size
            await self.alchemy_util_obj.insert(product_model)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def update_product(self, product_dict: Dict[str, Any]):
        """This function is to update the product."""
        try:
            if "product_id" not in product_dict:
                raise Exception("Product Id not found.")
            product_id = product_dict["product_id"]
            update_stmt = (
                update(Product)
                .where(Product.product_id == product_id)
                .values(
                    **product_dict
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc

    async def delete_product(self, product_id):
        """This function is to delete nfc."""
        try:
            update_stmt = (
                update(Product)
                .where(Product.product_id == product_id)
                .values(is_active=False)
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc
        
    
    async def reset_nfc(self):
        """"This function is to reset nfc."""
        try:
            update_stmt = (
                update(Nfc)
                .values(
                    {
                        "usability": 0
                    }
                )
            )
            await self.alchemy_util_obj.update(update_stmt)
        except Exception as exc:
            print(str(exc))
            raise exc
        
        
    async def validate_nfc(self, nfc_id, device_id, mqtt_util_obj):
        """This function is to validate nfc."""
        try:
            nfc_stmt = select(Nfc).where(Nfc.nfc_id == nfc_id)
            nfc_result = await self.alchemy_util_obj.execute_one_with_one(nfc_stmt)
            machine_vendor_stmt = select(MachineVendor).where(MachineVendor.device_id == device_id)
            mv_result = await self.alchemy_util_obj.execute_one_with_one(machine_vendor_stmt)
            if nfc_result and nfc_result.usability < nfc_result.frequency:
                usability_update = nfc_result.usability + 1
                purchase_histories = []
                purchase_history = {
                    "usability": usability_update
                }
                if nfc_result.purchase_history:
                    purchase_histories = nfc_result.purchase_history
                purchase_histories.append(purchase_history)
                update_stmt = (
                    update(Nfc)
                    .where(Nfc.nfc_id == nfc_id)
                    .values(
                        {
                            "usability": usability_update, 
                            "purchase_history": purchase_histories
                         }
                    )
                )
                await self.alchemy_util_obj.update(update_stmt)
                payload = {
                    "device_id": device_id,
                    "command": "nfc_sack",
                    "dispatch": "true",
                    "payment": "success"
                }
                await mqtt_util_obj.publish(mv_result.topic_to_subscribe, payload)
            else:
                payload = {
                    "device_id": device_id,
                    "command": "nfc_sack",
                    "dispatch": "false",
                    "payment": "failure"
                }
                await mqtt_util_obj.publish(mv_result.topic_to_subscribe, payload)
        except Exception as exc:
            print(str(exc))
            raise exc


    async def get_payments_report(self, filters: PaymentReportParams):
        """This function is to get payments report."""
        try:
            conditions = []
            conditions.append(Transaction.status == "Completed")
            if "vendor_id" in filters and filters["vendor_id"] and filters["vendor_id"].strip() != "all":
                conditions.append(MachineVendor.vendor_id == filters["vendor_id"])
            if "payment_type" in filters and filters["payment_type"] and filters["payment_type"].strip() != "all":
                conditions.append(Transaction.payment_type == filters["payment_type"])
            if "from_date" in filters and filters["from_date"] and "to_date" in filters and filters["to_date"]:
                conditions.append(Transaction.updated_at.between(filters["from_date"], filters["to_date"]))
            where_stmt = and_(*conditions)
            if "from_string" in filters and "to_string" in filters:
                stmt = (
                    select(
                        MachineVendor.vendor_id,
                        Vendor.company_name,
                        Transaction.payment_type,
                        func.count(Transaction.transaction_id).label("transaction_count"),
                        (func.count(Transaction.transaction_id) * Stock.product_price).label("total_value")
                    )
                    .join(Stock, MachineVendor.machine_vendor_id == Stock.machine_vendor_id)
                    .join(Vendor, MachineVendor.vendor_id == Vendor.vendor_id)
                    .join(Transaction, MachineVendor.machine_id == Transaction.machine_id)  # Join Stock with Transaction
                    .where(where_stmt)  # Filter for completed transactions
                    .group_by(Stock.product_id)  # Group by relevant fields
                )
                print(stmt)
                result = await self.alchemy_util_obj.execute_all(stmt)
                combined_results = []
                for iterated_data in result:
                    formatted_data = {
                        "vendor_id": iterated_data[0],
                        "vendor_name": iterated_data[1],
                        "payment_type": iterated_data[2],
                        "transaction_count": iterated_data[3],
                        "total_value": iterated_data[4],
                        "from_string": filters["from_string"],
                        "to_string": filters["to_string"]
                    }
                    combined_results.append(formatted_data)
            else:
                stmt = (
                    select(
                        MachineVendor.vendor_id,
                        Vendor.company_name,
                        Transaction.payment_type,
                        func.count(Transaction.transaction_id).label("transaction_count"),
                        (func.count(Transaction.transaction_id) * Stock.product_price).label("total_value"),
                        cast(func.date_format(Transaction.updated_at, "%Y-%m-01"), String).label("month_start"),  # First day of the month
                        (
                            cast(func.last_day(Transaction.updated_at), String)  # Last day of the month
                        ).label("month_end")
                    )
                    .join(Stock, MachineVendor.machine_vendor_id == Stock.machine_vendor_id)
                    .join(Vendor, MachineVendor.vendor_id == Vendor.vendor_id)
                    .join(Transaction, MachineVendor.machine_id == Transaction.machine_id)  # Join Stock with Transaction
                    .where(where_stmt)  # Filter for completed transactions
                    .group_by(MachineVendor.vendor_id, Vendor.company_name, Transaction.payment_type, func.date_format(Transaction.updated_at, "%Y-%m-01"))  # Group by relevant fields
                )
                print(stmt)
                result = await self.alchemy_util_obj.execute_all(stmt)
                combined_results = []
                for iterated_data in result:
                    formatted_data = {
                        "vendor_id": iterated_data[0],
                        "vendor_name": iterated_data[1],
                        "payment_type": iterated_data[2],
                        "transaction_count": iterated_data[3],
                        "total_value": iterated_data[4],
                        "from_string": iterated_data[5],
                        "to_string": iterated_data[6]
                    }
                    combined_results.append(formatted_data)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc


    async def get_sales_report(self, filters: SalesReportParams):
        """This function is to get sales report."""
        try:
            conditions = []
            conditions.append(Transaction.status == "Completed")
            if "vendor_id" in filters and filters["vendor_id"] and filters["vendor_id"].strip() != "all":
                conditions.append(MachineVendor.vendor_id == filters["vendor_id"])
            if "product_id" in filters and filters["product_id"] and filters["product_id"].strip() != "all":
                conditions.append(Stock.product_id == filters["product_id"])
            if "from_date" in filters and filters["from_date"] and "to_date" in filters and filters["to_date"]:
                conditions.append(Transaction.updated_at.between(filters["from_date"], filters["to_date"]))
            where_stmt = and_(*conditions)
            stmt = (
                select(
                    MachineVendor.machine_id,
                    Stock.product_id,
                    Product.product_name,
                    Stock.product_price,
                    func.count(Transaction.transaction_id).label("transaction_count"),
                    (func.count(Transaction.transaction_id) * Stock.product_price).label("total_value"),
                    func.date(Transaction.created_at).label("transaction_date")
                )
                .join(Stock, MachineVendor.machine_vendor_id == Stock.machine_vendor_id)  # Join MachineVendor with Stock
                .join(Product, Product.product_id == Stock.product_id)  # Join MachineVendor with Stock
                .join(Transaction, MachineVendor.machine_id == Transaction.machine_id)  # Join Stock with Transaction
                .where(where_stmt)  # Filter for completed transactions
                .group_by(func.date(Transaction.created_at), Transaction.machine_id)  # Group by relevant fields
            )
            print(stmt)
            result = await self.alchemy_util_obj.execute_all(stmt)
            combined_results = []
            for iterated_data in result:
                formatted_data = {
                    "machine_id": iterated_data[0],
                    "product_id": iterated_data[1],
                    "product_name": iterated_data[2],
                    "product_price": iterated_data[3],
                    "transaction_count": iterated_data[4],
                    "total_value": iterated_data[5],
                    "transaction_date": iterated_data[6].isoformat()
                }
                combined_results.append(formatted_data)
            return combined_results
        except Exception as exc:
            print(str(exc))
            raise exc
        

    async def get_dashboard_data(self, filters: DashboardParams):
        """This function is to get dashboard data."""
        try:
            vendor_id = filters.vendor_id
            vendor_data = []
            # Get total number of vendors
            if vendor_id and vendor_id.strip() != "all":
                # If filtering by specific vendor, total vendors = 1
                total_vendors = 1
            else:
                # Count all active vendors
                total_vendors_stmt = select(func.count(func.distinct(Vendor.vendor_id))).select_from(Vendor).where(Vendor.is_active == True)
                total_vendors_result = await self.alchemy_util_obj.execute_one(total_vendors_stmt)
                total_vendors = total_vendors_result[0] if total_vendors_result else 0

            # Get total number of machines
            if vendor_id and vendor_id.strip() != "all":
                # Count machines for specific vendor
                total_machines_stmt = (
                    select(func.count(func.distinct(MachineVendor.machine_id)))
                    .select_from(MachineVendor)
                    .join(Machine, MachineVendor.machine_id == Machine.machine_id)
                    .where(and_(MachineVendor.vendor_id == vendor_id, Machine.is_active == True))
                )
            else:
                # Count all active machines
                total_machines_stmt = (
                    select(func.count(func.distinct(Machine.machine_id)))
                    .select_from(Machine)
                    .where(Machine.is_active == True)
                )

            total_machines_result = await self.alchemy_util_obj.execute_one(total_machines_stmt)
            total_machines = total_machines_result[0] if total_machines_result else 0

            # Prepare the response
            dashboard_data = {
                "vendor_data": vendor_data,
                "total_vendors": total_vendors,
                "total_machines": total_machines
            }

            return dashboard_data

        except Exception as exc:
            print(str(exc))
            raise exc
