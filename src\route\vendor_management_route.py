from src.service.vendor_management_service import VendorManagementService
from fastapi import APIRouter, Depends
from src.util.auth_util import PermissionCheck


class VendorManagementRoute:
    """This class acts as an route for Vendor Management."""

    def __init__(self, vm_service_obj: VendorManagementService):
        """This is a init function to initialize."""
        self.vm_service_obj = vm_service_obj
        self.router = APIRouter()

    async def load_routes(self):
        self.router.add_api_route(
            "/vendor",
            self.vm_service_obj.create_vendor,
            methods=["POST"],
            tags=["Vendor"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/vendor",
            self.vm_service_obj.update_vendor,
            methods=["PUT"],
            tags=["Vendor"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/vendor/{vendor_id}",
            self.vm_service_obj.get_vendor_by_id,
            methods=["GET"],
            tags=["Vendor"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/vendors",
            self.vm_service_obj.get_vendors,
            methods=["GET"],
            tags=["Vendor"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/vendor/{vendor_id}",
            self.vm_service_obj.delete_vendor,
            methods=["PUT"],
            tags=["Vendor"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/users",
            self.vm_service_obj.get_users,
            methods=["GET"],
            tags=["User"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/user/{username}",
            self.vm_service_obj.get_user,
            methods=["GET"],
            tags=["User"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/user",
            self.vm_service_obj.create_user,
            methods=["POST"],
            tags=["User"],
        )
        self.router.add_api_route(
            "/user",
            self.vm_service_obj.update_user,
            methods=["PUT"],
            tags=["User"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/user/role",
            self.vm_service_obj.update_user_role,
            methods=["PUT"],
            tags=["User"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/user",
            self.vm_service_obj.delete_user,
            methods=["PUT"],
            tags=["User"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/machines",
            self.vm_service_obj.get_machines,
            methods=["GET"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/machines",
            self.vm_service_obj.get_machines_by,
            methods=["POST"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/machine/qrcode/{machine_id}",
            self.vm_service_obj.get_qr_code,
            methods=["GET"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/machine/qrcode/{machine_id}/{product_id}",
            self.vm_service_obj.regenerate_qr_code,
            methods=["PUT"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/machine",
            self.vm_service_obj.create_machine,
            methods=["POST"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/machine",
            self.vm_service_obj.update_machine,
            methods=["PUT"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/machine/{machine_id}",
            self.vm_service_obj.delete_machine,
            methods=["PUT"],
            tags=["Machine"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        self.router.add_api_route(
            "/stock/{stock_id}",
            self.vm_service_obj.get_stock_by_id,
            methods=["GET"],
            tags=["Stock"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/stocks",
            self.vm_service_obj.get_stocks,
            methods=["GET"],
            tags=["Stock"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/stock",
            self.vm_service_obj.create_stock,
            methods=["POST"],
            tags=["Stock"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/stock",
            self.vm_service_obj.delete_stock,
            methods=["PUT"],
            tags=["Stock"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/feedback/{purchase_id}",
            self.vm_service_obj.get_purchase_by_id,
            methods=["GET"],
            tags=["Feedback"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/feedbacks",
            self.vm_service_obj.get_purchases,
            methods=["GET"],
            tags=["Feedback"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/feedback",
            self.vm_service_obj.delete_purchase,
            methods=["PUT"],
            tags=["Feedback"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/mqtt/logs",
            self.vm_service_obj.get_mqtt_logs,
            methods=["GET"],
            tags=["Mqtt"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfcs",
            self.vm_service_obj.get_nfcs,
            methods=["GET"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfc/{nfc_id}",
            self.vm_service_obj.get_nfc,
            methods=["GET"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfc/upload",
            self.vm_service_obj.upload_nfc,
            methods=["POST"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfc",
            self.vm_service_obj.create_nfc,
            methods=["POST"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfc",
            self.vm_service_obj.update_nfc,
            methods=["PUT"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/nfc/{nfc_id}",
            self.vm_service_obj.delete_nfc,
            methods=["PUT"],
            tags=["Nfc"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/orders/{machine_id}",
            self.vm_service_obj.get_orders_by_machine,
            methods=["GET"],
            tags=["Order"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/products",
            self.vm_service_obj.get_products,
            methods=["GET"],
            tags=["Product"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/products/{vendor_id}",
            self.vm_service_obj.get_products_by_vendor,
            methods=["GET"],
            tags=["Product"]
        )
        self.router.add_api_route(
            "/product",
            self.vm_service_obj.create_product,
            methods=["POST"],
            tags=["Product"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/product",
            self.vm_service_obj.update_product,
            methods=["PUT"],
            tags=["Product"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/report/sales",
            self.vm_service_obj.get_sales_report,
            methods=["POST"],
            tags=["Report"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/report/payments",
            self.vm_service_obj.get_payments_report,
            methods=["POST"],
            tags=["Report"],
            dependencies=[Depends(PermissionCheck("super_admin|vendor_admin_1"))],
        )
        self.router.add_api_route(
            "/dashboard",
            self.vm_service_obj.get_dashboard_data,
            methods=["POST"],
            tags=["Report"],
            dependencies=[Depends(PermissionCheck("super_admin"))],
        )
        return self.router
