from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
import warnings
from src.util.cors_middleware import DynamicCORSMiddleware
from src.util.auth_middleware import AuthMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.exceptions import RequestValidationError
from starlette.middleware import Middleware
from contextlib import asynccontextmanager
from fastapi.exception_handlers import (
    http_exception_handler,
    request_validation_exception_handler,
)
from fastapi.security.api_key import API<PERSON>ey<PERSON>eader
import uvicorn
from src.service.vendor_management_service import VendorManagementService
from src.route.vendor_management_route import VendorManagementRoute
from src.route.independent_route import IndependentRoute
from src.schedule.vendor_management_scheduler import VendorManagementScheduler
import secrets
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.middleware import SlowAPIMiddleware
from slowapi.errors import RateLimitExceeded
from fastapi.responses import JSONResponse

environment = "development"
enable_ssl = False
port = 7331

warnings.filterwarnings("error")
api_key_header = APIKeyHeader(name="Authorization")
secret_key = "sad"#secrets.token_hex(32)

def get_device_id(request: Request):
    """Extracts device ID from headers, falls back to IP if unavailable."""
    remote_address = get_remote_address(request)
    print(remote_address)
    device_id = request.headers.get("X-Device-ID", remote_address)
    print(device_id)
    return device_id


@asynccontextmanager
async def lifespan(vm_app: FastAPI):
    limiter = Limiter(key_func=get_device_id)
    vm_app.state.limiter = limiter
    vm_service_obj = VendorManagementService(secret_key)
    independent_route_obj = IndependentRoute(vm_service_obj, limiter)
    independent_router = await independent_route_obj.load_routes()
    vm_app.include_router(independent_router, prefix="/vendor/management")
    vm_router_obj = VendorManagementRoute(vm_service_obj)
    vm_router = await vm_router_obj.load_routes()
    vm_app.include_router(
        vm_router,
        prefix="/vendor/management/service",
        dependencies=[Depends(api_key_header)],
    )
    # vm_scheduler_obj = VendorManagementScheduler()
    # await vm_scheduler_obj.schedule(vm_service_obj)
    yield

    vm_service_obj.shutdown()


my_pre_ordered_middlewares = [
    Middleware(DynamicCORSMiddleware),
    Middleware(AuthMiddleware, secret_key=secret_key),
    Middleware(SlowAPIMiddleware)
]
vm_app = FastAPI(lifespan=lifespan, middleware=my_pre_ordered_middlewares)


@vm_app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(
    request: Request, exception: StarletteHTTPException
):
    return await http_exception_handler(request, exception)


@vm_app.exception_handler(RequestValidationError)
async def custom_request_exception_handler(
    request: Request, exception: RequestValidationError
):
    return await request_validation_exception_handler(request, exception)

# Rate limit error handler
@vm_app.exception_handler(RateLimitExceeded)
async def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
    return JSONResponse(
        status_code=429,
        content={"detail": "Rate limit exceeded. Try again later."},
    )


def get_vm_app():
    return vm_app


def get_envrt():
    return environment


if __name__ == "__main__":
    if enable_ssl:
        uvicorn.run(
            "app:vm_app", 
            host="0.0.0.0", 
            port=443, 
            ssl_keyfile="key.pem", 
            ssl_certfile="cert.pem", 
            reload=False)
    else:
        uvicorn.run(
            "app:vm_app", 
            host="0.0.0.0", 
            port=port,
            reload=False)
