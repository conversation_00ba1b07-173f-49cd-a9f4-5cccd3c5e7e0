import razorpay
import hmac
import hashlib


class RazorPayUtil:
    """Util to handle razor pay payments."""

    def __init__(self, config_dict):
        """This is to initialize the class."""
        self.key_id = config_dict["key_id"]
        self.key_secret = config_dict["key_secret"]
        self.client = razorpay.Client(auth=(self.key_id, self.key_secret))

    async def get_client(key_id, key_secret):
        if key_id and key_secret:
            return razorpay.Client(auth=(key_id, key_secret))
        return self.client
        

    async def create_order(self, amountInRs, receipt, client):
        """Function to create order."""
        order_data = {
            "amount": amountInRs
            * 100,  # amount in the smallest currency unit (e.g., paise)
            "currency": "INR",
            "payment_capture": 0,  # auto capture
            "receipt": receipt
        }
        print(order_data)
        return client.order.create(data=order_data)

    async def capture_payment(self, payment_id, amountInRs, client):
        """Function to capture payment."""
        capture_amount = (
            amountInRs * 100
        )  # amount in the smallest currency unit (e.g., paise)
        return client.payment.capture(payment_id, capture_amount)

    async def fetch_payment(self, payment_id, client):
        """Function to fetch payment."""
        return client.payment.fetch(payment_id)

    async def refund_payment(self, payment_id, refundAmountInRs, client):
        """Function to refund payment."""
        refund_data = {
            "amount": refundAmountInRs
            * 100  # amount in the smallest currency unit (e.g., paise)
        }
        return client.payment.refund(payment_id, data=refund_data)

    async def verify_signature(self, secret_key, order_id, payment_id, received_signature):
        # Combine parameters
        data = f"{payment_id}|{order_id}"
        # Compute HMAC with SHA256
        generated_signature = hmac.new(
            secret_key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        print(generated_signature)
        print(received_signature)
        
        # Compare the generated signature with the received signature
        return hmac.compare_digest(generated_signature, received_signature)