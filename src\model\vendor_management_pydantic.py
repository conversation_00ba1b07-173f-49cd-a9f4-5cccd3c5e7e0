from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict
from pydantic_sqlalchemy import sqlalchemy_to_pydantic
from src.model.vendor_management_alchemy import (
    User,
    UserRole,
    Role,
    MachineVendor,
    Machine,
    Product,
    Stock,
    Purchase,
    MqttLog,
    Transaction,
    Vendor,
    Nfc
)
import json

# Convert SQLAlchemy models to Pydantic models
PydanticUser = sqlalchemy_to_pydantic(User)
PydanticUserRole = sqlalchemy_to_pydantic(UserRole)
PydanticRole = sqlalchemy_to_pydantic(Role)
PydanticMachineVendor = sqlalchemy_to_pydantic(MachineVendor)
PydanticMachine = sqlalchemy_to_pydantic(Machine)
PydanticProduct = sqlalchemy_to_pydantic(Product)
PydanticStock = sqlalchemy_to_pydantic(Stock)
PydanticPurchase = sqlalchemy_to_pydantic(Purchase)
PydanticMqttLog = sqlalchemy_to_pydantic(MqttLog)
PydanticTransaction = sqlalchemy_to_pydantic(Transaction)
PydanticVendor = sqlalchemy_to_pydantic(Vendor)

BaseNfc = sqlalchemy_to_pydantic(Nfc)

class PydanticNfc(BaseNfc):
    purchase_history: Optional[List[Dict]] = None

class DetailedVendor(BaseModel):
    vendor: PydanticVendor

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "vendor_id": self.vendor.vendor_id,
            "company_name": self.vendor.company_name,
            "gst": self.vendor.gst,
            "address_1": self.vendor.address_1,
            "address_2": self.vendor.address_2,
            "city": self.vendor.city,
            "state": self.vendor.state,
            "pincode": self.vendor.pincode,
            "phone_number": self.vendor.phone_number,
            "email_id": self.vendor.email_id,
            "is_active": self.vendor.is_active,
            "created_at": self.vendor.created_at.isoformat()
        }

class DetailedUserRoleWithVendor(BaseModel):
    user: PydanticUser
    user_role: Optional[PydanticUserRole]
    role: Optional[PydanticRole]
    vendor: Optional[PydanticVendor]

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "username": self.user.username,
            "firstname": self.user.firstname,
            "lastname": self.user.lastname,
            "is_active": self.user.is_active,
            "vendor_id": self.user.vendor_id,
            "vendor_name": self.vendor.company_name if self.vendor else None,
            "role": self.role.role_name,
            "email_id": self.user.email_id,
            "created_at": self.user.created_at.isoformat(),
            "updated_at": self.user.updated_at.isoformat(),
        }

class DetailedUserRole(BaseModel):
    user: PydanticUser
    user_role: Optional[PydanticUserRole]
    role: Optional[PydanticRole]

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "username": self.user.username,
            "firstname": self.user.firstname,
            "lastname": self.user.lastname,
            "is_active": self.user.is_active,
            "vendor_id": self.user.vendor_id,
            "role": self.role.role_name,
            "email_id": self.user.email_id,
            "phone_number": self.user.phone_number,
            "created_at": self.user.created_at.isoformat(),
            "updated_at": self.user.updated_at.isoformat(),
        }


class DetailedMachineVendorAndStock(BaseModel):
    machine_vendor: PydanticMachineVendor
    machine: PydanticMachine
    stock: PydanticStock
    product: PydanticProduct
    vendor: PydanticVendor

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "machine_id": self.machine_vendor.machine_id,
            "is_active": self.machine.is_active,
            "purchase_date": self.machine.purchase_date.isoformat() if self.machine.purchase_date else self.machine.purchase_date,
            "latitude": self.machine.latitude,
            "longitude": self.machine.longitude,
            "warranty": self.machine.warranty,
            "expiry_date": self.machine.expiry_date,
            "model_number": self.machine.model_number,
            "serial_number": self.machine.serial_number,
            "imei_number": self.machine.imei_number,
            "sim_number": self.machine.sim_number,
            "vendor_id": self.machine_vendor.vendor_id,
            "vendor_name": self.vendor.company_name,
            "qr_code": self.machine_vendor.qr_code,
            "topic_to_publish": self.machine_vendor.topic_to_publish,
            "topic_to_subscribe": self.machine_vendor.topic_to_subscribe,
            "device_id": self.machine_vendor.device_id,
            "mqtt_username": self.machine_vendor.mqtt_username,
            "product_sold": self.stock.product_sold,
            "availability": self.stock.availability,
            "capacity": self.stock.capacity,
            "product_price": self.stock.product_price,
            "product_id": self.product.product_id,
            "product_name": self.product.product_name,
            "product_image": self.product.image,
            "price": self.stock.product_price,
            "created_at": self.machine_vendor.created_at.isoformat(),
            "updated_at": self.machine_vendor.updated_at.isoformat()
        }

class DetailedMachineVendor(BaseModel):
    machine_vendor: PydanticMachineVendor
    machine: PydanticMachine

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "machine_id": self.machine_vendor.machine_id,
            "is_active": self.machine.is_active,
                        "purchase_date": self.machine.purchase_date,
            "latitude": self.machine.latitude,
            "longitude": self.machine.longitude,
            "warranty": self.machine.warranty,
            "expiry_date": self.machine.expiry_date,
            "model_number": self.machine.model_number,
            "serial_number": self.machine.serial_number,
            "imei_number": self.machine.imei_number,
            "sim_number": self.machine.sim_number,
            "vendor_id": self.machine_vendor.vendor_id,
            "device_id": self.machine_vendor.device_id,
            "qr_code": self.machine_vendor.qr_code,
            "device_id": self.machine_vendor.device_id,
            "topic_to_publish": self.machine_vendor.topic_to_publish,
            "topic_to_subscribe": self.machine_vendor.topic_to_subscribe,
            "mqtt_username": self.machine_vendor.mqtt_username,
            "created_at": self.machine_vendor.created_at.isoformat(),
            "updated_at": self.machine_vendor.updated_at.isoformat()
        }

class DetailedStock(BaseModel):
    stock: PydanticStock
    product: PydanticProduct
    machine_vendor: PydanticMachineVendor

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "stock_id": self.stock.stock_id,
            "vendor_id": self.machine_vendor.vendor_id,
            "device_id": self.machine_vendor.device_id,
            "product_name": self.product.product_name,
            "product_image": self.product.image,
            "specification": self.product.specification,
            "price": self.stock.product_price,
            "size": self.product.size,
            "product_name": self.product.product_name,
            "availability": self.stock.availability,
            "capacity": self.stock.capacity,
            "created_at": self.stock.created_at.isoformat(),
            "updated_at": self.stock.updated_at.isoformat(),
        }


class DetailedPurchase(BaseModel):
    purchase: PydanticPurchase
    stock: PydanticStock

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "purchase_id": self.purchase.purchase_id,
            "feedback": self.purchase.feedback,
            "rating": self.purchase.rating,
            "stock_id": self.stock.stock_id,
            "created_at": self.stock.created_at.isoformat(),
            "updated_at": self.stock.updated_at.isoformat(),
        }

class DetailedMqttLog(BaseModel):
    mqttlog: PydanticMqttLog

    class Config:
        orm_mode = True

    async def read(self):
        if self.mqttlog.payload:
            # Step 1: Remove the outer quotes by using json.loads
            inner_json_str = json.loads(self.mqttlog.payload)
            # Step 2: Parse the inner JSON string into a dictionary
            payload_dict = json.loads(inner_json_str)
        else:
            payload_dict = None
        return {
            "mqtt_log_id": self.mqttlog.mqtt_log_id,
            "status": self.mqttlog.status.value if self.mqttlog.status else None,
            "payload": payload_dict,
            "topic_name": self.mqttlog.topic_name,
            "created_at": self.mqttlog.created_at.isoformat(),
            "updated_at": self.mqttlog.updated_at.isoformat(),
            "completed_at": self.mqttlog.completed_at.isoformat(),
        }

class DetailedNfc(BaseModel):
    nfc: PydanticNfc
    machine_vendor: PydanticMachineVendor

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "nfc_id": self.nfc.nfc_id,
            "name": self.nfc.name,
            "frequency_type": self.nfc.frequency_type,
            "frequency": self.nfc.frequency,
            "purchase_history": self.nfc.purchase_history if self.nfc.purchase_history else [],
            "is_active": self.nfc.is_active,
            "machine_id": self.nfc.machine_id,
            "vendor_id": self.machine_vendor.vendor_id,
            "created_at": self.nfc.created_at.isoformat(),
            "updated_at": self.nfc.updated_at.isoformat(),
        }


class DetailedTransaction(BaseModel):
    transaction: PydanticTransaction
    product: PydanticProduct
    stock: PydanticStock

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "transaction_id": self.transaction.transaction_id,
            "order": self.transaction.order,
            "status": self.transaction.status,
            "payment_id": self.transaction.payment_id,
            "payment_type": self.transaction.payment_type,
            "payment_status": self.transaction.payment_status,
            "machine_id": self.transaction.machine_id,
            "product_name": self.product.product_name,
            "product_price": self.stock.product_price,
            "created_at": self.transaction.created_at.isoformat(),
            "updated_at": self.transaction.updated_at.isoformat(),
        }

class DetailedProduct(BaseModel):
    product: PydanticProduct

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "product_id": self.product.product_id,
            "product_name": self.product.product_name,
            "image": self.product.image,
            "size": self.product.size,
            "is_active": self.product.is_active,
            "specification": self.product.specification,
            "created_at": self.product.created_at.isoformat(),
            "updated_at": self.product.updated_at.isoformat(),
        }

class DetailedProductAndStock(BaseModel):
    product: PydanticProduct
    stock: PydanticStock
    machine_vendor: PydanticMachineVendor

    class Config:
        orm_mode = True

    async def read(self):
        return {
            "product_id": self.product.product_id,
            "product_name": self.product.product_name,
            "price": self.stock.product_price,
            "image": self.product.image,
            "size": self.product.size,
            "availability": self.stock.availability,
            "machine_id": self.machine_vendor.machine_id,
            "specification": self.product.specification
        }


class LoginUser(BaseModel):
    """This class is a base model for login user."""

    username: str
    password: str


class UserRolePy(BaseModel):
    """This class is a base model for user role."""

    username: str
    role: str

class CreateVendor(BaseModel):
    """This class is a base model for Vendor creation."""

    company_name: str
    gst: str
    address_1: str
    address_2: str
    city: str
    state: str
    pincode: str
    phone_number: str
    email_id: str

class CreateUser(BaseModel):
    """This class is a base model for User creation."""

    username: str
    password: str
    firstname: str
    lastname: str
    vendor_id: str
    role_id: str
    email_id: str
    phone_number: str

class PaymentReportParams(BaseModel):
    """This class is a base model for payment report."""

    vendor_id: str
    payment_type: str
    from_date: str
    to_date: str

class SalesReportParams(BaseModel):
    """This class is a base model for sales report."""

    vendor_id: str
    product_id: str
    from_date: str
    to_date: str

class DashboardParams(BaseModel):
    """This class is a base model for dashboard."""

    vendor_id: str
    from_date: str
    to_date: str

class CreateProduct(BaseModel):
    """This class is a base model for Product creation."""

    product_name: str
    price: str
    image: str
    size: str
    specification: str

class UpdateUser(BaseModel):
    """This class is a base model for User updation."""

    username: str
    password: str
    firstname: str
    lastname: str
    email_id: str
    vendor_id: str


class CreateMachine(BaseModel):
    """This class is a base model for Machine."""

    vendor_id: str
    product_id: str
    device_id: str
    mqtt_username: str
    mqtt_password: str
    purchase_date: str
    latitude: str
    longitude: str
    warranty: str
    expiry_date: str
    model_number: str
    serial_number: str
    imei_number: str
    sim_number: str

class UpdateMachine(BaseModel):
    """This class is a base model for Machine."""

    machine_id: int
    username: str


class CreateStock(BaseModel):
    """This class is a base model for Stock."""

    stock_name: str
    machine_id: str
    vendor_id: str
    product_id: str
    availability: int
    capacity: int
    price: str

class UpdateStock(BaseModel):
    """This class is a base model for Stock."""

    stock_id: str
    availability: str


class CreatePurchase(BaseModel):
    """This class is a base model for purchase"""

    feedback: str
    stock_id: int
    rating: str

class CreateNfc(BaseModel):
    """This class is a base model for Nfc creation."""

    name: str
    frequency_type: str
    frequency: int
    machine_id: str