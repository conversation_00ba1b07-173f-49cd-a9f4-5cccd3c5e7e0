# MQTT Dev Setup

Step 1: Ensure that Mos<PERSON>tto is installed in your system. 

    https://mosquitto.org/download/
    https://mosquitto.org/files/binary/win64/mosquitto-2.0.18a-install-windows-x64.exe
    Run the installer in windows machine

Step 2: Configure a password for the MQTT broker.

    mosquitto_passwd -c /path/to/passwordfile username

Step 3: Modify the config of mosquitto setup.

    C:\Program Files\mosquitto\mosquitto.conf
        allow_anonymous false - Not to allow anonymous users to access
        password_file /path/to/passwordfile
                    C:\Program files\mosquitto\passwdfile
        log_dest stderr
        connection_messages true

Step 4: Start the mosquitto server/broker.

    mosquitto -c "C:\Program files\mosquitto\mosquitto.conf"

Note:

    mosquitto_pub -h <broker_host> -t <topic> -m <message> -u <username> -P <password>
    mosquitto_pub -h localhost -t vms/machine_001_karurkannan_pub -m "{\"message\":320}" -u machine_001 -P machine_001
    mosquitto_sub -h localhost -t vms/machine_001_karurkannan_sub -u machine_001 -P machine_001
