import qrcode
from io import BytesIO


class VendorManagementUtil:
    """This class acts as an util for Vendor Management."""

    def __init__(self):
        """This is a init function to initialize."""

    async def generate_qr_code(self, machine_id):
        qr = qrcode.QRCode(
            version=1,  # Version 1: 21x21 matrix (more versions available, higher versions mean larger size and more data)
            error_correction=qrcode.constants.ERROR_CORRECT_L,  # Error correction level
            box_size=10,  # Size of each box in the QR code grid
            border=4,  # Thickness of the border (minimum is 4)
        )
        data = f"http://portal.goinstagood.com/machine/{machine_id}"
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image(fill="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        qr_code_bytes = buffer.getvalue()
        return qr_code_bytes
