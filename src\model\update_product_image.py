import mysql.connector

# Connect to MySQL
conn = mysql.connector.connect(
    host="***************",
    user="u994973241_admin",
    password="We56@r5L!49Uy#Xgy65%e",
    database="u994973241_instagood"
)
cursor = conn.cursor()

# Read the new BLOB file
with open("src/model/bag.png", "rb") as file:
    new_blob = file.read()

# Update the BLOB content
sql = "UPDATE products SET image = %s WHERE product_id = %s"
cursor.execute(sql, (new_blob, 1))

# Commit changes
conn.commit()

print("BLOB updated successfully!")

# Close connection
cursor.close()
conn.close()