self.router.add_api_route(
            "/machines",
            self.vm_service_obj.get_machines,
            methods=["GET"],
            tags=["Machine"],
        )
        self.router.add_api_route(
            "/machine",
            self.vm_service_obj.create_machine,
            methods=["POST"],
            tags=["Machine"],
        )
        self.router.add_api_route(
            "/machine",
            self.vm_service_obj.update_machine,
            methods=["PUT"],
            tags=["Machine"],
        )
        self.router.add_api_route(
            "/machine/{machine_id}",
            self.vm_service_obj.delete_machine,
            methods=["DELETE"],
            tags=["Machine"],
        )