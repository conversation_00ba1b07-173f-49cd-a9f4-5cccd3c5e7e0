from fastapi.responses import JSONResponse, StreamingResponse
from src.connector.vendor_management_connector import VendorManagementConnector
from src.model.vendor_management_pydantic import (
    CreateUser,
    CreateMachine,
    UpdateMachine,
    LoginUser,
    UserRolePy,
    UpdateUser,
    CreateStock,
    UpdateStock,
    CreatePurchase,
    CreateVendor,
    CreateProduct,
    CreateNfc,
    SalesReportParams,
    PaymentReportParams,
    DashboardParams,
)
from io import BytesIO
from starlette.requests import Request
from src.util.mqtt_util import MQTTUtil
from src.util.custom_exception_handler import CustomException<PERSON>andler
from typing import Dict, Any
import asyncio
from fastapi import File, UploadFile


class VendorManagementService:
    """This class acts as an service for Vendor Management."""

    def __init__(self, secret_key):
        """This is a init function to initialize."""
        self.vm_connector_obj = VendorManagementConnector(secret_key)
        self.mqtt_util_obj = MQTTUtil(self.vm_connector_obj)
        self.cus_exc_han = CustomExceptionHandler()

    def shutdown(self):
        self.mqtt_util_obj.shutdown()

    async def validate_user_gen_token(self, user_data: LoginUser):
        """This function is to validate the user and generate jwt token."""
        try:
            user = await self.vm_connector_obj.validate_user(
                user_data.username, user_data.password
            )
            if user is None:
                return JSONResponse(
                    status_code=404, content="Please verify the inputs."
                )
            token = await self.vm_connector_obj.generate_token(user)
            return JSONResponse(status_code=200, content=token)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_vendor(self, vendor: CreateVendor):
        """This function is to create new vendor."""
        try:
            success_msg = "Vendor creation completed successfully."
            await self.vm_connector_obj.create_vendor(vendor)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_vendor(self, vendor_dict: Dict[str, Any]):
        """This function is to update user."""
        try:
            success_msg = "Vendor updation completed successfully."
            await self.vm_connector_obj.update_vendor(vendor_dict)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_vendors(self):
        """This function is to get all vendors."""
        try:
            vendors = await self.vm_connector_obj.get_vendors()
            return JSONResponse(status_code=200, content=vendors)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_vendor_by_id(self, vendor_id: str):
        """This function is to get all vendor."""
        try:
            vendor = await self.vm_connector_obj.get_vendor_by_id(vendor_id)
            return JSONResponse(status_code=200, content=vendor)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_vendor(self, vendor_id: str):
        """This function is to delete vendor."""
        try:
            success_msg = "Vendor deletion completed successfully."
            await self.vm_connector_obj.delete_vendor(vendor_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_users(self):
        """This function is to get all users."""
        try:
            users = await self.vm_connector_obj.get_users()
            return JSONResponse(status_code=200, content=users)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_user(self, username: str):
        """This function is to get user."""
        try:
            users = await self.vm_connector_obj.get_user(username)
            return JSONResponse(status_code=200, content=users)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_user(self, user: CreateUser):
        """This function is to create new user."""
        try:
            success_msg = "User creation completed successfully."
            await self.vm_connector_obj.create_user(user)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_user(self, user_dict: Dict[str, Any]):
        """This function is to update user."""
        try:
            success_msg = "User updation completed successfully."
            await self.vm_connector_obj.update_user(user_dict)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_user_role(self, user: UserRolePy):
        """This function is to update user role."""
        try:
            success_msg = "User updation completed successfully."
            await self.vm_connector_obj.update_user_role(user)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_user(self, username: str):
        """This function is to delete user."""
        try:
            success_msg = "User deletion completed successfully."
            await self.vm_connector_obj.delete_user(username)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_machine_activeness(self, machine_id, request: Request):
        """This function is to get product activeness."""
        try:
            machine_details = await self.vm_connector_obj.get_machine_subscribed_topic(machine_id)
            machine_subscribed_topic = machine_details["topic_to_subscribe"]
            device_id = machine_details["device_id"]
            if machine_id in self.vm_connector_obj.product_availabilities:
                del self.vm_connector_obj.product_availabilities[machine_id]
            payload = {
                "command": "getcount",
                "device_id": device_id
            }
            await self.mqtt_util_obj.publish(machine_subscribed_topic, payload)
            activeness = await self.check_activeness(machine_id=machine_id)
            print(activeness)
            return JSONResponse(status_code=200, content=activeness)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))
        
    async def check_activeness(self, machine_id, timeout=30, interval=1):
        start_time = asyncio.get_running_loop().time()
        
        while asyncio.get_running_loop().time() - start_time < timeout:
            try:
                if self.vm_connector_obj.product_availabilities.get(str(machine_id), False):
                    return {"is_active": True}
                else:
                    print("Machine activeness not found. Waiting...")
            except Exception:
                print("Failure in finding Machine activeness.")
            
            await asyncio.sleep(interval)  # Wait before checking again

        return {"is_active": False}  # Return False if timeout reached

    async def get_machines(self):
        """This function is to get all machines."""
        try:
            machines = await self.vm_connector_obj.get_machines()
            return JSONResponse(status_code=200, content=machines)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_machines_by(self, machine_detail: Dict[str, Any]):
        """This function is to get machines by."""
        try:
            machine = await self.vm_connector_obj.get_machines_by(machine_detail)
            return JSONResponse(status_code=200, content=machine)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_qr_code(self, machine_id: str):
        """This function is to get all machines."""
        try:
            qr_code_bytes = await self.vm_connector_obj.get_qr_code(machine_id)
            return StreamingResponse(BytesIO(qr_code_bytes), media_type="image/png")
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_machine(self, machine_dict: Dict[str, Any]):
        """This function is to update machine."""
        try:
            success_msg = "Machine updation completed successfully."
            await self.vm_connector_obj.update_machine(machine_dict)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_machine(self, machine: CreateMachine):
        """This function is to create new machine."""
        try:
            success_msg = "Machine creation completed successfully."
            machine_details = await self.vm_connector_obj.create_machine(machine)
            topic_to_subscribe = machine_details["topic_to_subscribe"]
            machine_id = machine_details["machine_id"]
            return_obj = {
                "success_msg": success_msg,
                "machine_id": machine_id
            }
            await self.mqtt_util_obj.subscribe_new_topic(topic_to_subscribe)
            return JSONResponse(status_code=200, content=return_obj)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def regenerate_qr_code(self, machine_id: str):
        """This function is to regenerate qr code."""
        try:
            qr_code_bytes = await self.vm_connector_obj.regenerate_qr_code(machine_id)
            return StreamingResponse(BytesIO(qr_code_bytes), media_type="image/png")
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_machine(self, machine_id: int):
        """This function is to delete machine."""
        try:
            success_msg = "Machine deletion completed successfully."
            await self.vm_connector_obj.delete_machine(machine_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_stocks(self):
        """This function is to get all stocks."""
        try:
            stocks = await self.vm_connector_obj.get_stocks()
            return JSONResponse(status_code=200, content=stocks)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_stock_by_id(self, stock_id: str):
        """This function is to get a single stock."""
        try:
            stock = await self.vm_connector_obj.get_stock_by_id(stock_id)
            return JSONResponse(status_code=200, content=stock)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_stock(self, stock: CreateStock):
        """This function is to create new stock."""
        try:
            success_msg = "Stock creation completed successfully."
            await self.vm_connector_obj.create_stock(stock)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_stock(self, stock: UpdateStock):
        """This function is to update stock."""
        try:
            success_msg = "Stock updateion completed successfully."
            await self.vm_connector_obj.update_stock_availability(stock)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_stock(self, stock_id: int):
        """This function is to delete stock."""
        try:
            success_msg = "Stock deletion completed successfully."
            await self.vm_connector_obj.delete_stock(stock_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_purchases(self):
        """This function is to get all purchases."""
        try:
            purchases = await self.vm_connector_obj.get_purchases()
            return JSONResponse(status_code=200, content=purchases)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_purchase_by_id(self, purchase_id: str):
        """This function is to get a single purchase."""
        try:
            purchase = await self.vm_connector_obj.get_purchase_by_id(purchase_id)
            return JSONResponse(status_code=200, content=purchase)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_purchase(self, purchase: CreatePurchase, request: Request):
        """This function is to create new purchase."""
        try:
            success_msg = "Purchase creation completed successfully."
            await self.vm_connector_obj.create_purchase(purchase)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_purchase(self, purchase_id: int):
        """This function is to delete purchase."""
        try:
            success_msg = "Purchase deletion completed successfully."
            await self.vm_connector_obj.delete_purchase(purchase_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def publish(self, topic_name, payload: dict, request: Request):
        """This function is to publish mqtt messages."""
        try:
            success_msg = "Message has been published successfully."
            await self.mqtt_util_obj.publish(topic_name, payload)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_order(self, order: dict, request: Request):
        """This function is to create new order."""
        try:
            success_msg = "Order creation completed successfully."
            order = await self.vm_connector_obj.create_order(order)
            content = {
                "order": order,
                "message": success_msg
            }
            return JSONResponse(status_code=200, content=content)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_order(self, order: dict, request: Request):
        """This function is to update order."""
        try:
            success_msg = "Order updation completed successfully."
            await self.vm_connector_obj.update_order(order, self.mqtt_util_obj)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_mqtt_logs(self):
        """This function is to get mqtt logs."""
        try:
            mqtt_logs = await self.vm_connector_obj.get_mqtt_logs()
            return JSONResponse(status_code=200, content=mqtt_logs)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_nfcs(self):
        """This function is to get nfcs."""
        try:
            nfcs = await self.vm_connector_obj.get_nfcs()
            return JSONResponse(status_code=200, content=nfcs)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_nfc(self, nfc_id):
        """This function is to get nfc by nfc_id."""
        try:
            nfc = await self.vm_connector_obj.get_nfc(nfc_id)
            return JSONResponse(status_code=200, content=nfc)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def upload_nfc(self, file: UploadFile = File(...)):
        # Validate file type
        if file.content_type != "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            raise HTTPException(status_code=400, detail="Invalid file format. Please upload an Excel file.")
        try:
            success_msg = "Nfc upload completed successfully."
            await self.vm_connector_obj.upload_nfc(file)
            return JSONResponse(status_code=200, content=success_msg)
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_nfc(self, create_nfc: CreateNfc):
        """This function is to create nfc."""
        try:
            success_msg = "Nfc creation completed successfully."
            await self.vm_connector_obj.create_nfc(create_nfc)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_nfc(self, nfc_dict: Dict[str, Any]):
        """This function is to update nfc."""
        try:
            success_msg = "Nfc updation completed successfully."
            await self.vm_connector_obj.update_nfc(nfc_dict)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_nfc(self, nfc_id):
        """This function is to get nfc."""
        try:
            success_msg = "Nfc deletion completed successfully."
            await self.vm_connector_obj.delete_nfc(nfc_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_orders_by_machine(self, machine_id):
        """This function is to get all machines."""
        try:
            orders = await self.vm_connector_obj.get_orders_by_machine(machine_id)
            return JSONResponse(status_code=200, content=orders)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_products(self):
        """This function is to get all products."""
        try:
            products = await self.vm_connector_obj.get_products()
            return JSONResponse(status_code=200, content=products)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_product_by_machine(self, machine_id, request: Request):
        """This function is to get product by machine."""
        try:
            product = await self.vm_connector_obj.get_product_by_machine(machine_id)
            return JSONResponse(status_code=200, content=product)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_products_by_vendor(self, vendor_id):
        """This function is to get product by vendor."""
        try:
            products = await self.vm_connector_obj.get_products_by_vendor(vendor_id)
            return JSONResponse(status_code=200, content=products)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def create_product(self, product: CreateProduct):
        """This function is to create new vendor."""
        try:
            success_msg = "Product creation completed successfully."
            await self.vm_connector_obj.create_product(product)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def update_product(self, product_dict: Dict[str, Any]):
        """This function is to update product."""
        try:
            success_msg = "Product updation completed successfully."
            await self.vm_connector_obj.update_product(product_dict)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def delete_product(self, product_id: str):
        """This function is to delete vendor."""
        try:
            success_msg = "Product deletion completed successfully."
            await self.vm_connector_obj.delete_product(product_id)
            return JSONResponse(status_code=200, content=success_msg)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_payments_report(self, filters: PaymentReportParams):
        """This function is to get payments report."""
        try:
            report = await self.vm_connector_obj.get_payments_report(filters)
            return JSONResponse(status_code=200, content=report)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))

    async def get_sales_report(self, filters: SalesReportParams):
        """This function is to get sales report."""
        try:
            report = await self.vm_connector_obj.get_sales_report(filters)
            return JSONResponse(status_code=200, content=report)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))
    
    async def reset_nfc(self):
        """This function is to reset nfc."""
        try:
            report = await self.vm_connector_obj.reset_nfc()
            return JSONResponse(status_code=200, content=report)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))
        
    async def get_dashboard_data(self, filters: DashboardParams):
        """This function is to get dashboard data."""
        try:
            report = await self.vm_connector_obj.get_dashboard_data(filters)
            return JSONResponse(status_code=200, content=report)
        except RuntimeWarning as warn:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_warning(warn))
        except RuntimeError as err:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_error(err))
        except Exception as exc:
            return JSONResponse(status_code=500, content=self.cus_exc_han.handle_exception(exc))
