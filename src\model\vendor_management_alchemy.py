from sqlalchemy import (
    create_engine,
    Column,
    Table,
    Integer,
    Float,
    String,
    ForeignKey,
    DateTime,
    func,
    join,
    LargeBinary,
    Enum as SqlEnum,
    Boolean,
    Text,
    JSON
)
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from urllib.parse import quote_plus
from enum import Enum
from bcrypt import hashpw, gensalt, checkpw
from pydantic import EmailStr
import base64

Base = declarative_base()

class Role(Base):
    __tablename__ = "roles"

    role_id = Column(Integer, primary_key=True, index=True)
    role_name = Column(String(100), unique=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    user_roles = relationship("UserRole", uselist=True, back_populates="role")

class Vendor(Base):
    __tablename__ = "vendors"

    vendor_id = Column(String(100), primary_key=True, index=True)
    company_name = Column(String(100), unique=True)
    gst = Column(String(100), unique=True)
    address_1 = Column(String(100))
    address_2 = Column(String(100))
    city = Column(String(100))
    state = Column(String(100))
    pincode = Column(String(100))
    phone_number = Column(String(100), unique=True)
    email_id = Column(String(100), unique=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    users = relationship("User", cascade="all, delete-orphan")
    machine_vendor = relationship("MachineVendor", back_populates="vendor")

class User(Base):
    __tablename__ = "users"

    username = Column(String(100), primary_key=True, index=True)
    password = Column(String(100))
    firstname = Column(String(100))
    lastname = Column(String(100))
    email_id = Column(String(100), unique=True)
    phone_number = Column(String(100), unique=True)
    is_active = Column(Boolean, default=True)
    vendor_id = Column(String(100), ForeignKey("vendors.vendor_id"), nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    user_role = relationship(
        "UserRole", uselist=False, back_populates="user", cascade="all, delete-orphan"
    )
    vendor = relationship("Vendor", back_populates="users")

    def set_password(self, plain_password: str):
        """
        Hashes a plain text password and stores it.
        """
        self.password = hashpw(plain_password.encode('utf-8'), gensalt()).decode('utf-8')

    def verify_password(self, plain_password: str) -> bool:
        """
        Verifies a plain text password against the stored hashed password.
        """
        return checkpw(plain_password.encode('utf-8'), self.password.encode('utf-8'))

class UserRole(Base):
    __tablename__ = "user_roles"

    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(Integer, ForeignKey("roles.role_id"), nullable=False)
    username = Column(
        String(100), ForeignKey("users.username"), unique=True, nullable=False
    )
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    user = relationship("User", back_populates="user_role")
    role = relationship("Role", back_populates="user_roles")


class Machine(Base):
    __tablename__ = "machines"

    machine_id = Column(Integer, primary_key=True, index=True)
    is_active = Column(Boolean, default=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    purchase_date = Column(DateTime)
    latitude = Column(String(100))
    longitude = Column(String(100))
    warranty = Column(String(100))
    expiry_date = Column(String(100))
    model_number = Column(String(100))
    serial_number = Column(String(100), unique=True)
    imei_number = Column(String(100), unique=True)
    sim_number = Column(String(100), unique=True)
    machine_vendors = relationship(
        "MachineVendor",
        uselist=False,
        back_populates="machine",
        cascade="all, delete-orphan",
    )
    nfcs = relationship(
        "Nfc",
        uselist=False,
        back_populates="machine",
        cascade="all, delete-orphan"
    )
    transactions = relationship(
        "Transaction",
        uselist=False,
        back_populates="machine",
        cascade="all, delete-orphan"
    )
    
class Nfc(Base):
    __tablename__ = "nfcs"

    nfc_id = Column(String(100), primary_key=True)
    name = Column(String(100), nullable=False)
    frequency_type = Column(String(100), nullable=False)
    frequency = Column(Integer, nullable=False)
    usability = Column(Integer, nullable=False)
    purchase_history = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True)
    machine_id = Column(Integer, ForeignKey("machines.machine_id"), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    machine = relationship("Machine", back_populates="nfcs")

class MachineVendor(Base):
    __tablename__ = "machine_vendors"

    machine_vendor_id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("machines.machine_id"), nullable=False)
    vendor_id = Column(
        String(100), ForeignKey("vendors.vendor_id"), nullable=False
    )
    qr_code = Column(LargeBinary)
    device_id = Column(String(100), nullable=False)
    topic_to_publish = Column(String(100), nullable=False)
    topic_to_subscribe = Column(String(100), nullable=False)
    mqtt_username = Column(String(100), nullable=False)
    mqtt_password = Column(String(100), nullable=False)
    razorpay_key = Column(String(100))
    razorpay_secret = Column(String(100))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    vendor = relationship("Vendor", back_populates="machine_vendor")
    machine = relationship("Machine", back_populates="machine_vendors")
    stocks = relationship(
        "Stock",
        uselist=False,
        back_populates="machine_vendor",
        cascade="all, delete-orphan",
    )

    def set_mqtt_password(self, plain_password: str):
        """
        Hashes a plain text password and stores it.
        """
        self.mqtt_password = hashpw(plain_password.encode('utf-8'), gensalt()).decode('utf-8')


association_table = Table(
    "association",
    Base.metadata,
    Column("product_id", Integer, ForeignKey("products.product_id")),
    Column("stock_id", Integer, ForeignKey("stocks.stock_id")),
)


class Product(Base):
    __tablename__ = "products"

    product_id = Column(Integer, primary_key=True, index=True)
    product_name = Column(String(100), unique=True)
    image = Column(LargeBinary(length=2**32-1))
    specification = Column(Text)
    size = Column(String(100))
    is_active = Column(Boolean, default=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    stocks = relationship(
        "Stock", secondary=association_table, back_populates="products"
    )


class Stock(Base):
    __tablename__ = "stocks"

    stock_id = Column(Integer, primary_key=True, index=True)
    machine_vendor_id = Column(
        Integer, ForeignKey("machine_vendors.machine_vendor_id"), nullable=False
    )
    product_id = Column(Integer, ForeignKey("products.product_id"), nullable=False)
    availability = Column(Integer)
    capacity = Column(Integer)
    product_sold = Column(Integer)
    product_price = Column(Float)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    products = relationship(
        "Product", secondary=association_table, back_populates="stocks"
    )
    machine_vendor = relationship("MachineVendor", back_populates="stocks")
    purchases = relationship("Purchase", back_populates="stock")


class Purchase(Base):
    __tablename__ = "purchases"

    purchase_id = Column(Integer, primary_key=True, index=True)
    feedback = Column(String(250))
    rating = Column(String(50))
    stock_id = Column(Integer, ForeignKey("stocks.stock_id"), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    stock = relationship("Stock", back_populates="purchases")

class MqttLogStatus(Enum):
    NEW = "new"
    PROCESSING = "processing"
    COMPLETE = "completed"
    FAIL = "failed"

class MqttLog(Base):
    __tablename__ = "mqtt_logs_updated"

    mqtt_log_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    payload = Column(String(15000), nullable=False)
    topic_name = Column(String(100))
    status = Column(
        SqlEnum(MqttLogStatus, name="mqtt_log_status", native_enum=False, values_callable=lambda x: [e.name for e in x]), 
        nullable=False, 
        index=True
    )
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    completed_at = Column(DateTime)

class Transaction(Base):
    __tablename__ = "transactions"

    transaction_id = Column(String(200), primary_key=True, index=True)
    order = Column(String(15000), nullable=False)
    payment_status = Column(String(100))
    status = Column(String(100))
    payment_id = Column(String(100))
    payment_type = Column(String(100), nullable=False)
    machine_id = Column(Integer, ForeignKey("machines.machine_id"), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, server_default=func.now())
    machine = relationship("Machine", back_populates="transactions")

class AlchemyUtil:

    def convert_image(self, image_path):
        # Open the image file in binary mode and encode it to Base64
        with open(image_path, "rb") as image_file:
            base64_bytes = base64.b64encode(image_file.read())
        return base64_bytes

    def __init__(self, config_dict):
        username = config_dict["user"]
        password = config_dict["password"]
        host = config_dict["host"]
        database = config_dict["database"]
        enable_drop_create = config_dict["enable_drop_create"]
        # URL encode the password
        encoded_password = quote_plus(password)

        database_url = (
            f"mysql+pymysql://{username}:{encoded_password}@{host}/{database}"
        )
        engine = create_engine(database_url, pool_size=10, max_overflow=20, pool_pre_ping=True)
        self.connection = engine.raw_connection()
        self.session_local = sessionmaker(
            autocommit=False, autoflush=False, bind=engine
        )
        if enable_drop_create:
            Base.metadata.drop_all(bind=engine)
            Base.metadata.create_all(bind=engine)

            super_admin_role = Role()
            super_admin_role.role_name = "super_admin"
            vendor_admin1_role = Role()
            vendor_admin1_role.role_name = "vendor_admin_1"
            vendor_admin2_role = Role()
            vendor_admin2_role.role_name = "vendor_admin_2"
            roles = [super_admin_role, vendor_admin1_role, vendor_admin2_role]

            bag_product = Product()
            bag_product.product_name = "Cloth Bag"
            bag_product.specification = '[{"title": "Cloth bag size", "value": "4x16x3 inch"}, {"title": "Cloth bag thickness", "value": "100-135 GSM"}, {"title": "Cloth bag capacity", "value": "5-8 kgs"}]'
            bag_product.size = "M"
            bag_product.image = self.convert_image("src/model/bag.jpg")
            bottle_product = Product()
            bottle_product.product_name = "Water Bottle"
            bottle_product.price = 10.00
            bottle_product.specification = '[{"title": "Water bottle thickness", "value": "50-60 GSM"}, {"title": "Water bottle capacity", "value": "1 litres"}]'
            bottle_product.size = "M"
            bottle_product.image = self.convert_image("src/model/bottle.jpeg")
            products = [bag_product, bottle_product]

            session = self.session_local()
            try:
                session.add_all(roles + products)
                session.commit()
            finally:
                session.close()
