import paho.mqtt.client as mqtt
import threading
import json
from datetime import datetime
from src.model.vendor_management_alchemy import MqttLogStatus
import asyncio
import subprocess

# from src.connector.vendor_management_connector import VendorManagementConnector


class MQTTUtil:
    """Util to handle MQTT transactions."""

    def __init__(self, vm_connector_obj):
        self.vm_connector_obj = vm_connector_obj
        config_dict = vm_connector_obj.config_dict
        self.broker = config_dict["broker"]
        self.acl_file = config_dict["acl_file"]
        self.port = config_dict["port"]
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)
        self.client.enable_logger()
        self.client.username_pw_set(
            username=config_dict["mqtt_username"], password=config_dict["mqtt_password"]
        )

        # Assign callback functions
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect

        # self.start()
        mqtt_thread = threading.Thread(target=self.start)
        mqtt_thread.start()

    # Define callback functions
    def on_connect(self, client, userdata, flags, reasonCode, properties=None):
        print(f"MQTT Reason code {reasonCode}")
        loop = asyncio.new_event_loop()

        if reasonCode == "Success":
            self.topics_to_subscribe = loop.run_until_complete(self.vm_connector_obj.get_topics_to_subscribe())
            for topic in self.topics_to_subscribe:
                print(f"Subscribing to {topic}")
                client.subscribe(topic, qos=2)
            self.get_new_and_process(loop)
        else:
            raise Exception("Connection not established with MQTT")

    async def subscribe_new_topic(self, topic):
        print(f"Subscribing to {topic}")
        self.client.subscribe(topic, qos=2)

    def on_message(self, client, userdata, msg):
        print(f"Message received: Topic: {msg.topic}, Message: {msg.payload.decode()}")
        topic_payload = json.loads(msg.payload)
        loop = asyncio.new_event_loop()
        if 'unique_id' in topic_payload and 'payload' in topic_payload:
            unique_id = topic_payload['unique_id']
            payload = topic_payload['payload']
        else:
            payload = topic_payload
            mqtt_log = loop.run_until_complete(self.vm_connector_obj.create_mqtt_log(msg.topic, json.dumps(payload)))
            print(f"MQTT Log created: {mqtt_log}")
            unique_id = mqtt_log.mqtt_log_id
        self.process(msg.topic, unique_id, payload, loop)

    def get_new_and_process(self, loop):
        results = loop.run_until_complete(self.vm_connector_obj.get_new_mqtt_logs())
        for result in results:
            if 'mqtt_log_id' in result and 'payload' in result and 'topic_name' in result and result['topic_name'] in self.topics_to_subscribe:
                print(f"Message received: Topic: {result['topic_name']}, Message: {result['payload']}")
                self.process(result['topic_name'], result['mqtt_log_id'], result['payload'], loop)

    def process(self, topic, unique_id, payload, loop):
        try:
            print("Processing message...")
            loop.run_until_complete(self.vm_connector_obj.update_mqtt_log(unique_id, MqttLogStatus.PROCESSING))
            # Implementation for MQTT executions.
            print("Validating NFC...")
            loop.run_until_complete(self.vm_connector_obj.init_nfc_validation(topic, payload, self))
            print("Updating stock availability...")
            loop.run_until_complete(self.vm_connector_obj.update_stock_availability_from_mqtt(topic, payload))
            print("Updating MQTT log...")
            loop.run_until_complete(self.vm_connector_obj.complete_mqtt_log(unique_id))
            print("Message subscription succeeded.")
        except Exception as exc:
            # loop.run_until_complete(self.vm_connector_obj.update_mqtt_log(unique_id, MqttLogStatus.FAIL))
            print("Message subscription failed.")
            print(str(exc))


    def on_disconnect(self, client, userdata, reasonCode, properties=None, extras=None):
        print(f"Disconnected with reason code {reasonCode}")

    def start(self):
        self.client.connect(self.broker, self.port, 60)
        self.client.loop_forever()

    def shutdown(self):
        self.client.disconnect()

    async def publish(self, topic_name, payload):
        timestamp = int(datetime.now().timestamp())
        topic_payload = {
            "unique_id": timestamp,
            "payload": payload
        }
        print(f"Message published: Topic: {topic_name}, Message: {payload}")
        mqtt_log = await self.vm_connector_obj.create_mqtt_log(topic_name, json.dumps(payload))
        result = self.client.publish(topic_name, json.dumps(payload), qos=2)
        result.rc  # Check result code, 0 means success
        print(json.dumps(payload))
        print(result.rc)
        if result.rc == 0:
            print("Message publication succeeded.")
            await self.vm_connector_obj.complete_mqtt_log(mqtt_log.mqtt_log_id)
        else:
            err_msg = "Message publication failed."
            print(err_msg)
            await self.vm_connector_obj.update_mqtt_log(mqtt_log.mqtt_log_id, MqttLogStatus.FAIL)
            raise Exception(err_msg)

async def add_mqtt_user(username, password, password_file):
    """Add a new user to the Mosquitto password file."""
    try:
        # Add the user to the Mosquitto password file
        subprocess.run(['mosquitto_passwd', '-b', password_file, username, password], check=True)
        print(f"User '{username}' added successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Failed to add user '{username}': {e}")

async def reload_mqtt_broker(broker, username, password, new_user):
    try:
        #subprocess.run(['mosquitto_pub', '-h', broker, '-t', "$SYS/broker/command/reload", '-m', "RELOAD", "-u", username, "-P", password], check=True)
        subprocess.run(
            ['sudo', 'systemctl', 'restart', 'mosquitto'],
            check=True,
            text=True,
            capture_output=True
        )
    except subprocess.CalledProcessError as e:
        print(f"Failed to reload mqtt broker for user '{new_user}': {e}")


async def update_acl_file(username, topics, acl_file):
    """Update the ACL file with the topics a user can access, or modify existing topics."""
    contents = {}
    comments = []
    
    with open(acl_file, 'r') as acl:
        lines = acl.readlines()
        current_user_line = ""
        for line in lines:
            if "user" in line:
                contents[line] = []
                current_user_line = line
            elif "topic" in line:
                contents[current_user_line].append(line)
            elif "#" in line:
                comments.append(line)

    collective_user_lines = list(contents.keys())
    new_user_line = f"user {username}\n"

    if(new_user_line in collective_user_lines):
        collective_topic_data = contents[new_user_line]
        for topic, access in topics.items():
            matching_line_arr = [line for line in collective_topic_data if topic in line]
            if(matching_line_arr):
                collective_topic_data.remove(matching_line_arr[0])
            collective_topic_data.append(f"topic {access} {topic}\n")
        contents[new_user_line] = collective_topic_data
    else:
        collective_topic_data = []
        for topic, access in topics.items():
            collective_topic_data.append(f"topic {access} {topic}\n")
        contents[new_user_line] = collective_topic_data
        comments.append(f"# This affects access control for {username}\n")

    with open(acl_file, 'w') as acl:
        user_index = 0
        for user_line, topic_lines in contents.items():
            acl.write(comments[user_index])
            acl.write(user_line)
            for topic_line in topic_lines:
                acl.write(topic_line)
            user_index = user_index + 1
